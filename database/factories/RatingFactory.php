<?php

namespace Database\Factories;

use App\Models\Rating;
use App\Models\Institute;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Rating>
 */
class RatingFactory extends Factory
{
    public function definition(): array
    {
        $rateableTypes = [
            Institute::class,
        ];
        return [
            'user_id' => UserFactory::new(),
            'comment' => $this->faker->optional()->sentence,
            'avg_score'  => $this->faker->numberBetween(1, 5),
            'rateable_type' => $this->faker->randomElement($rateableTypes),
            'rateable_id' => $this->faker->randomNumber(),
        ];
    }
}
