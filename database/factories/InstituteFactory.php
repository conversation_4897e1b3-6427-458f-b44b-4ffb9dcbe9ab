<?php

namespace Database\Factories;

use App\Enums\InstituteStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Institute>
 */
class InstituteFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id'         => UserFactory::new(),
            'name'            => json_encode([
                'en' => $this->faker->company,
                'ar' => $this->faker->company,
            ]),
            'institute_email' => $this->faker->unique()->safeEmail,
            'dialing_code'    => '+966',
            'avg_rating'      => $this->faker->randomFloat(1, 0, 5),
            'total_comments'  => $this->faker->numberBetween(0, 100),
            'is_verified'     => $this->faker->boolean,
            'status'          => InstituteStatus::Approved,
        ];
    }
}
