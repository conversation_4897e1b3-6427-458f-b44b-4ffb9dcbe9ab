<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Doctor>
 */
class DoctorFactory extends Factory
{
    public function definition(): array
    {
        return [
            'salutation'    => json_encode(['en' => 'Dr.', 'ar' => 'د']),
            'first_name'    => json_encode(['en' => $this->faker->firstName, 'ar' => $this->faker->firstName]),
            'middle_name'   => json_encode(['en' => $this->faker->firstName, 'ar' => $this->faker->firstName]),
            'last_name'     => json_encode(['en' => $this->faker->lastName, 'ar' => $this->faker->lastName]),
            'full_name'     => json_encode(['en' => $this->faker->name, 'ar' => $this->faker->name]),
            'full_name_ar'  => $this->faker->name,
            'dialing_code'  => '+966',
            'other_phone'   => $this->faker->phoneNumber,
            'dob'           => $this->faker->date,
            'gender'        => $this->faker->randomElement(['male', 'female']),
            'profile_url'   => json_encode([
                'en' => $this->faker->slug,
                'ar' => $this->faker->slug,
            ]),
            'profile_photo' => $this->faker->imageUrl,
            'avg_rating'    => $this->faker->randomFloat(1, 0, 5),
            'rating_count'  => $this->faker->numberBetween(0, 100),
            'total_comments'=> $this->faker->numberBetween(0, 100),
            'visit_count'   => $this->faker->numberBetween(0, 1000),
            'status'        => 'approved',
            'about_me'      => json_encode([
                'en' => $this->faker->paragraph,
                'ar' => $this->faker->paragraph,
            ]),
        ];
    }
}
