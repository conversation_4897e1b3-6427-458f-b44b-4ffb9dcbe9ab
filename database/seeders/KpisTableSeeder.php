<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class KpisTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('kpis')->delete();
        
        \DB::table('kpis')->insert(array (
            0 => 
            array (
                'id' => 1,
                'kpi_no' => 1,
                'department_id' => 57,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'kpi_no' => 2,
                'department_id' => 57,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'kpi_no' => 3,
                'department_id' => 57,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'kpi_no' => 4,
                'department_id' => 57,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'kpi_no' => 5,
                'department_id' => 57,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'kpi_no' => 1,
                'department_id' => 58,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'kpi_no' => 2,
                'department_id' => 58,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'kpi_no' => 3,
                'department_id' => 58,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'kpi_no' => 4,
                'department_id' => 58,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'kpi_no' => 5,
                'department_id' => 58,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'kpi_no' => 1,
                'department_id' => 59,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'kpi_no' => 2,
                'department_id' => 59,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'kpi_no' => 3,
                'department_id' => 59,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'kpi_no' => 4,
                'department_id' => 59,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'kpi_no' => 5,
                'department_id' => 59,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 16,
                'kpi_no' => 1,
                'department_id' => 60,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'kpi_no' => 2,
                'department_id' => 60,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'kpi_no' => 3,
                'department_id' => 60,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'kpi_no' => 4,
                'department_id' => 60,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'kpi_no' => 5,
                'department_id' => 60,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'kpi_no' => 1,
                'department_id' => 61,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'kpi_no' => 2,
                'department_id' => 61,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'kpi_no' => 3,
                'department_id' => 61,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'kpi_no' => 4,
                'department_id' => 61,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'kpi_no' => 5,
                'department_id' => 61,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'kpi_no' => 1,
                'department_id' => 62,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            26 => 
            array (
                'id' => 27,
                'kpi_no' => 2,
                'department_id' => 62,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            27 => 
            array (
                'id' => 28,
                'kpi_no' => 3,
                'department_id' => 62,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            28 => 
            array (
                'id' => 29,
                'kpi_no' => 4,
                'department_id' => 62,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            29 => 
            array (
                'id' => 30,
                'kpi_no' => 5,
                'department_id' => 62,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            30 => 
            array (
                'id' => 31,
                'kpi_no' => 1,
                'department_id' => 63,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            31 => 
            array (
                'id' => 32,
                'kpi_no' => 2,
                'department_id' => 63,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            32 => 
            array (
                'id' => 33,
                'kpi_no' => 3,
                'department_id' => 63,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            33 => 
            array (
                'id' => 34,
                'kpi_no' => 4,
                'department_id' => 63,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            34 => 
            array (
                'id' => 35,
                'kpi_no' => 5,
                'department_id' => 63,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            35 => 
            array (
                'id' => 36,
                'kpi_no' => 1,
                'department_id' => 64,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            36 => 
            array (
                'id' => 37,
                'kpi_no' => 2,
                'department_id' => 64,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            37 => 
            array (
                'id' => 38,
                'kpi_no' => 3,
                'department_id' => 64,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            38 => 
            array (
                'id' => 39,
                'kpi_no' => 4,
                'department_id' => 64,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            39 => 
            array (
                'id' => 40,
                'kpi_no' => 5,
                'department_id' => 64,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            40 => 
            array (
                'id' => 41,
                'kpi_no' => 1,
                'department_id' => 65,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            41 => 
            array (
                'id' => 42,
                'kpi_no' => 2,
                'department_id' => 65,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            42 => 
            array (
                'id' => 43,
                'kpi_no' => 3,
                'department_id' => 65,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            43 => 
            array (
                'id' => 44,
                'kpi_no' => 4,
                'department_id' => 65,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            44 => 
            array (
                'id' => 45,
                'kpi_no' => 5,
                'department_id' => 65,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            45 => 
            array (
                'id' => 46,
                'kpi_no' => 1,
                'department_id' => 66,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            46 => 
            array (
                'id' => 47,
                'kpi_no' => 2,
                'department_id' => 66,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            47 => 
            array (
                'id' => 48,
                'kpi_no' => 3,
                'department_id' => 66,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            48 => 
            array (
                'id' => 49,
                'kpi_no' => 4,
                'department_id' => 66,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'kpi_no' => 5,
                'department_id' => 66,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'kpi_no' => 1,
                'department_id' => 68,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'kpi_no' => 2,
                'department_id' => 68,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'kpi_no' => 3,
                'department_id' => 68,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            53 => 
            array (
                'id' => 54,
                'kpi_no' => 4,
                'department_id' => 68,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            54 => 
            array (
                'id' => 55,
                'kpi_no' => 5,
                'department_id' => 68,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            55 => 
            array (
                'id' => 56,
                'kpi_no' => 1,
                'department_id' => 69,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            56 => 
            array (
                'id' => 57,
                'kpi_no' => 2,
                'department_id' => 69,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            57 => 
            array (
                'id' => 58,
                'kpi_no' => 3,
                'department_id' => 69,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            58 => 
            array (
                'id' => 59,
                'kpi_no' => 4,
                'department_id' => 69,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            59 => 
            array (
                'id' => 60,
                'kpi_no' => 5,
                'department_id' => 69,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            60 => 
            array (
                'id' => 61,
                'kpi_no' => 1,
                'department_id' => 70,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            61 => 
            array (
                'id' => 62,
                'kpi_no' => 2,
                'department_id' => 70,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            62 => 
            array (
                'id' => 63,
                'kpi_no' => 3,
                'department_id' => 70,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            63 => 
            array (
                'id' => 64,
                'kpi_no' => 4,
                'department_id' => 70,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            64 => 
            array (
                'id' => 65,
                'kpi_no' => 5,
                'department_id' => 70,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            65 => 
            array (
                'id' => 66,
                'kpi_no' => 1,
                'department_id' => 71,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            66 => 
            array (
                'id' => 67,
                'kpi_no' => 2,
                'department_id' => 71,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            67 => 
            array (
                'id' => 68,
                'kpi_no' => 3,
                'department_id' => 71,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            68 => 
            array (
                'id' => 69,
                'kpi_no' => 4,
                'department_id' => 71,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            69 => 
            array (
                'id' => 70,
                'kpi_no' => 5,
                'department_id' => 71,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            70 => 
            array (
                'id' => 71,
                'kpi_no' => 1,
                'department_id' => 72,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            71 => 
            array (
                'id' => 72,
                'kpi_no' => 2,
                'department_id' => 72,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            72 => 
            array (
                'id' => 73,
                'kpi_no' => 3,
                'department_id' => 72,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            73 => 
            array (
                'id' => 74,
                'kpi_no' => 4,
                'department_id' => 72,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            74 => 
            array (
                'id' => 75,
                'kpi_no' => 5,
                'department_id' => 72,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            75 => 
            array (
                'id' => 76,
                'kpi_no' => 1,
                'department_id' => 73,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            76 => 
            array (
                'id' => 77,
                'kpi_no' => 2,
                'department_id' => 73,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            77 => 
            array (
                'id' => 78,
                'kpi_no' => 3,
                'department_id' => 73,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            78 => 
            array (
                'id' => 79,
                'kpi_no' => 4,
                'department_id' => 73,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            79 => 
            array (
                'id' => 80,
                'kpi_no' => 5,
                'department_id' => 73,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            80 => 
            array (
                'id' => 81,
                'kpi_no' => 1,
                'department_id' => 74,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            81 => 
            array (
                'id' => 82,
                'kpi_no' => 2,
                'department_id' => 74,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            82 => 
            array (
                'id' => 83,
                'kpi_no' => 3,
                'department_id' => 74,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            83 => 
            array (
                'id' => 84,
                'kpi_no' => 4,
                'department_id' => 74,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            84 => 
            array (
                'id' => 85,
                'kpi_no' => 5,
                'department_id' => 74,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:02',
                'updated_at' => '2021-09-20 20:32:02',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            85 => 
            array (
                'id' => 86,
                'kpi_no' => 1,
                'department_id' => 75,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            86 => 
            array (
                'id' => 87,
                'kpi_no' => 2,
                'department_id' => 75,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            87 => 
            array (
                'id' => 88,
                'kpi_no' => 3,
                'department_id' => 75,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            88 => 
            array (
                'id' => 89,
                'kpi_no' => 4,
                'department_id' => 75,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            89 => 
            array (
                'id' => 90,
                'kpi_no' => 5,
                'department_id' => 75,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            90 => 
            array (
                'id' => 91,
                'kpi_no' => 1,
                'department_id' => 76,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            91 => 
            array (
                'id' => 92,
                'kpi_no' => 2,
                'department_id' => 76,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            92 => 
            array (
                'id' => 93,
                'kpi_no' => 3,
                'department_id' => 76,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            93 => 
            array (
                'id' => 94,
                'kpi_no' => 4,
                'department_id' => 76,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            94 => 
            array (
                'id' => 95,
                'kpi_no' => 5,
                'department_id' => 76,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            95 => 
            array (
                'id' => 96,
                'kpi_no' => 1,
                'department_id' => 77,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            96 => 
            array (
                'id' => 97,
                'kpi_no' => 2,
                'department_id' => 77,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            97 => 
            array (
                'id' => 98,
                'kpi_no' => 3,
                'department_id' => 77,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            98 => 
            array (
                'id' => 99,
                'kpi_no' => 4,
                'department_id' => 77,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            99 => 
            array (
                'id' => 100,
                'kpi_no' => 5,
                'department_id' => 77,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        \DB::table('kpis')->insert(array (
            0 => 
            array (
                'id' => 101,
                'kpi_no' => 1,
                'department_id' => 78,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 102,
                'kpi_no' => 2,
                'department_id' => 78,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 103,
                'kpi_no' => 3,
                'department_id' => 78,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 104,
                'kpi_no' => 4,
                'department_id' => 78,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 105,
                'kpi_no' => 5,
                'department_id' => 78,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 106,
                'kpi_no' => 1,
                'department_id' => 79,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 107,
                'kpi_no' => 2,
                'department_id' => 79,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 108,
                'kpi_no' => 3,
                'department_id' => 79,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 109,
                'kpi_no' => 4,
                'department_id' => 79,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 110,
                'kpi_no' => 5,
                'department_id' => 79,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 111,
                'kpi_no' => 1,
                'department_id' => 80,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 112,
                'kpi_no' => 2,
                'department_id' => 80,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 113,
                'kpi_no' => 3,
                'department_id' => 80,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 114,
                'kpi_no' => 4,
                'department_id' => 80,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 115,
                'kpi_no' => 5,
                'department_id' => 80,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 116,
                'kpi_no' => 1,
                'department_id' => 81,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 117,
                'kpi_no' => 2,
                'department_id' => 81,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 118,
                'kpi_no' => 3,
                'department_id' => 81,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 119,
                'kpi_no' => 4,
                'department_id' => 81,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 120,
                'kpi_no' => 5,
                'department_id' => 81,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 121,
                'kpi_no' => 1,
                'department_id' => 82,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 122,
                'kpi_no' => 2,
                'department_id' => 82,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 123,
                'kpi_no' => 3,
                'department_id' => 82,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 124,
                'kpi_no' => 4,
                'department_id' => 82,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 125,
                'kpi_no' => 5,
                'department_id' => 82,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            25 => 
            array (
                'id' => 126,
                'kpi_no' => 1,
                'department_id' => 83,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            26 => 
            array (
                'id' => 127,
                'kpi_no' => 2,
                'department_id' => 83,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            27 => 
            array (
                'id' => 128,
                'kpi_no' => 3,
                'department_id' => 83,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            28 => 
            array (
                'id' => 129,
                'kpi_no' => 4,
                'department_id' => 83,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            29 => 
            array (
                'id' => 130,
                'kpi_no' => 5,
                'department_id' => 83,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            30 => 
            array (
                'id' => 131,
                'kpi_no' => 1,
                'department_id' => 84,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            31 => 
            array (
                'id' => 132,
                'kpi_no' => 2,
                'department_id' => 84,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            32 => 
            array (
                'id' => 133,
                'kpi_no' => 3,
                'department_id' => 84,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            33 => 
            array (
                'id' => 134,
                'kpi_no' => 4,
                'department_id' => 84,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            34 => 
            array (
                'id' => 135,
                'kpi_no' => 5,
                'department_id' => 84,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            35 => 
            array (
                'id' => 136,
                'kpi_no' => 1,
                'department_id' => 86,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            36 => 
            array (
                'id' => 137,
                'kpi_no' => 2,
                'department_id' => 86,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            37 => 
            array (
                'id' => 138,
                'kpi_no' => 3,
                'department_id' => 86,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            38 => 
            array (
                'id' => 139,
                'kpi_no' => 4,
                'department_id' => 86,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            39 => 
            array (
                'id' => 140,
                'kpi_no' => 5,
                'department_id' => 86,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            40 => 
            array (
                'id' => 141,
                'kpi_no' => 1,
                'department_id' => 87,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            41 => 
            array (
                'id' => 142,
                'kpi_no' => 2,
                'department_id' => 87,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            42 => 
            array (
                'id' => 143,
                'kpi_no' => 3,
                'department_id' => 87,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            43 => 
            array (
                'id' => 144,
                'kpi_no' => 4,
                'department_id' => 87,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            44 => 
            array (
                'id' => 145,
                'kpi_no' => 5,
                'department_id' => 87,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            45 => 
            array (
                'id' => 146,
                'kpi_no' => 1,
                'department_id' => 88,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            46 => 
            array (
                'id' => 147,
                'kpi_no' => 2,
                'department_id' => 88,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            47 => 
            array (
                'id' => 148,
                'kpi_no' => 3,
                'department_id' => 88,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            48 => 
            array (
                'id' => 149,
                'kpi_no' => 4,
                'department_id' => 88,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            49 => 
            array (
                'id' => 150,
                'kpi_no' => 5,
                'department_id' => 88,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            50 => 
            array (
                'id' => 151,
                'kpi_no' => 1,
                'department_id' => 89,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            51 => 
            array (
                'id' => 152,
                'kpi_no' => 2,
                'department_id' => 89,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            52 => 
            array (
                'id' => 153,
                'kpi_no' => 3,
                'department_id' => 89,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            53 => 
            array (
                'id' => 154,
                'kpi_no' => 4,
                'department_id' => 89,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            54 => 
            array (
                'id' => 155,
                'kpi_no' => 5,
                'department_id' => 89,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            55 => 
            array (
                'id' => 156,
                'kpi_no' => 1,
                'department_id' => 90,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            56 => 
            array (
                'id' => 157,
                'kpi_no' => 2,
                'department_id' => 90,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            57 => 
            array (
                'id' => 158,
                'kpi_no' => 3,
                'department_id' => 90,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            58 => 
            array (
                'id' => 159,
                'kpi_no' => 4,
                'department_id' => 90,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            59 => 
            array (
                'id' => 160,
                'kpi_no' => 5,
                'department_id' => 90,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            60 => 
            array (
                'id' => 161,
                'kpi_no' => 1,
                'department_id' => 91,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            61 => 
            array (
                'id' => 162,
                'kpi_no' => 2,
                'department_id' => 91,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            62 => 
            array (
                'id' => 163,
                'kpi_no' => 3,
                'department_id' => 91,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            63 => 
            array (
                'id' => 164,
                'kpi_no' => 4,
                'department_id' => 91,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            64 => 
            array (
                'id' => 165,
                'kpi_no' => 5,
                'department_id' => 91,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            65 => 
            array (
                'id' => 166,
                'kpi_no' => 1,
                'department_id' => 92,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            66 => 
            array (
                'id' => 167,
                'kpi_no' => 2,
                'department_id' => 92,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            67 => 
            array (
                'id' => 168,
                'kpi_no' => 3,
                'department_id' => 92,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            68 => 
            array (
                'id' => 169,
                'kpi_no' => 4,
                'department_id' => 92,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            69 => 
            array (
                'id' => 170,
                'kpi_no' => 5,
                'department_id' => 92,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            70 => 
            array (
                'id' => 171,
                'kpi_no' => 1,
                'department_id' => 93,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            71 => 
            array (
                'id' => 172,
                'kpi_no' => 2,
                'department_id' => 93,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            72 => 
            array (
                'id' => 173,
                'kpi_no' => 3,
                'department_id' => 93,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            73 => 
            array (
                'id' => 174,
                'kpi_no' => 4,
                'department_id' => 93,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            74 => 
            array (
                'id' => 175,
                'kpi_no' => 5,
                'department_id' => 93,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            75 => 
            array (
                'id' => 176,
                'kpi_no' => 1,
                'department_id' => 94,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            76 => 
            array (
                'id' => 177,
                'kpi_no' => 2,
                'department_id' => 94,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            77 => 
            array (
                'id' => 178,
                'kpi_no' => 3,
                'department_id' => 94,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            78 => 
            array (
                'id' => 179,
                'kpi_no' => 4,
                'department_id' => 94,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            79 => 
            array (
                'id' => 180,
                'kpi_no' => 5,
                'department_id' => 94,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            80 => 
            array (
                'id' => 181,
                'kpi_no' => 1,
                'department_id' => 95,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            81 => 
            array (
                'id' => 182,
                'kpi_no' => 2,
                'department_id' => 95,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            82 => 
            array (
                'id' => 183,
                'kpi_no' => 3,
                'department_id' => 95,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            83 => 
            array (
                'id' => 184,
                'kpi_no' => 4,
                'department_id' => 95,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            84 => 
            array (
                'id' => 185,
                'kpi_no' => 5,
                'department_id' => 95,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            85 => 
            array (
                'id' => 186,
                'kpi_no' => 1,
                'department_id' => 96,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            86 => 
            array (
                'id' => 187,
                'kpi_no' => 2,
                'department_id' => 96,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            87 => 
            array (
                'id' => 188,
                'kpi_no' => 3,
                'department_id' => 96,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            88 => 
            array (
                'id' => 189,
                'kpi_no' => 4,
                'department_id' => 96,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            89 => 
            array (
                'id' => 190,
                'kpi_no' => 5,
                'department_id' => 96,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            90 => 
            array (
                'id' => 191,
                'kpi_no' => 1,
                'department_id' => 97,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            91 => 
            array (
                'id' => 192,
                'kpi_no' => 2,
                'department_id' => 97,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            92 => 
            array (
                'id' => 193,
                'kpi_no' => 3,
                'department_id' => 97,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            93 => 
            array (
                'id' => 194,
                'kpi_no' => 4,
                'department_id' => 97,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            94 => 
            array (
                'id' => 195,
                'kpi_no' => 5,
                'department_id' => 97,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            95 => 
            array (
                'id' => 196,
                'kpi_no' => 1,
                'department_id' => 98,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            96 => 
            array (
                'id' => 197,
                'kpi_no' => 2,
                'department_id' => 98,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            97 => 
            array (
                'id' => 198,
                'kpi_no' => 3,
                'department_id' => 98,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            98 => 
            array (
                'id' => 199,
                'kpi_no' => 4,
                'department_id' => 98,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            99 => 
            array (
                'id' => 200,
                'kpi_no' => 5,
                'department_id' => 98,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        \DB::table('kpis')->insert(array (
            0 => 
            array (
                'id' => 201,
                'kpi_no' => 1,
                'department_id' => 99,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 202,
                'kpi_no' => 2,
                'department_id' => 99,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 203,
                'kpi_no' => 3,
                'department_id' => 99,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 204,
                'kpi_no' => 4,
                'department_id' => 99,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 205,
                'kpi_no' => 5,
                'department_id' => 99,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 206,
                'kpi_no' => 1,
                'department_id' => 100,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 207,
                'kpi_no' => 2,
                'department_id' => 100,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 208,
                'kpi_no' => 3,
                'department_id' => 100,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 209,
                'kpi_no' => 4,
                'department_id' => 100,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 210,
                'kpi_no' => 5,
                'department_id' => 100,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 211,
                'kpi_no' => 1,
                'department_id' => 101,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 212,
                'kpi_no' => 2,
                'department_id' => 101,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 213,
                'kpi_no' => 3,
                'department_id' => 101,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 214,
                'kpi_no' => 4,
                'department_id' => 101,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 215,
                'kpi_no' => 5,
                'department_id' => 101,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 216,
                'kpi_no' => 1,
                'department_id' => 102,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 217,
                'kpi_no' => 2,
                'department_id' => 102,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 218,
                'kpi_no' => 3,
                'department_id' => 102,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 219,
                'kpi_no' => 4,
                'department_id' => 102,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 220,
                'kpi_no' => 5,
                'department_id' => 102,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 221,
                'kpi_no' => 1,
                'department_id' => 104,
                'title' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'title_remote' => '{"ar": "حسن التعامل", "en": "Politeness"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 222,
                'kpi_no' => 2,
                'department_id' => 104,
                'title' => '{"ar": "الخبرة", "en": "Experience"}',
                'title_remote' => '{"ar": "الخبرة", "en": "Experience"}',
                'countable' => '1.00',
                'weightage_doctor' => '70.00',
                'weightage_other' => '30.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 223,
                'kpi_no' => 3,
                'department_id' => 104,
                'title' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'title_remote' => '{"ar": "الطاقم الطبي", "en": "Staff"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 224,
                'kpi_no' => 4,
                'department_id' => 104,
                'title' => '{"ar": "الإستقبال", "en": "Reception"}',
                'title_remote' => '{"ar": "الإستقبال", "en": "Reception"}',
                'countable' => '1.00',
                'weightage_doctor' => '30.00',
                'weightage_other' => '70.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 225,
                'kpi_no' => 5,
                'department_id' => 104,
                'title' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'title_remote' => '{"ar": "دقة المواعيد", "en": "Punctuality"}',
                'countable' => '0.50',
                'weightage_doctor' => '50.00',
                'weightage_other' => '50.00',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:03',
                'updated_at' => '2021-09-20 20:32:03',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        
        
    }
}