<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SettingsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('settings')->delete();
        
        \DB::table('settings')->insert(array (
            0 => 
            array (
                'id' => 1,
                'business_name' => '{"ar": "قيم دكتوري", "en": "Qayim Dactory"}',
                'business_email' => '<EMAIL>',
                'business_dialing_code' => '+966',
                'business_phone' => '*********',
                'business_website' => 'qayimdactory.com',
                'business_address' => '{"ar": "شارع أبي دجانة، طريق الملك عبداللهحي المرسلات، الرياض 11573", "en": "Abu Dujana Street, King Abdullah Road, Al-Murasalat District, Riyadh 11573"}',
                'support_email' => '<EMAIL>',
                'support_dialing_code' => '+966',
                'support_phone' => '*********',
                'currency_code' => 'SAR',
                'language_code' => 'ar',
                'facebook_link' => 'http://facebook.com/qdactory',
                'instagram_link' => 'http://instagram.com/qdactory',
                'twitter_link' => 'http://twitter.com/qdactory',
                'youtube_link' => 'http://youtube.com/qdactory',
                'linkedin_link' => 'https://www.linkedin.com/company/qdactory/',
                'mail_host' => 'smtp.mailtrap.io',
                'mail_port' => '2525',
                'mail_user_name' => '93d4389f41d3a6',
                'mail_password' => '2f53a59285344d',
                'mail_encryption' => 'tls',
                'mail_from_name' => '{"ar": "قيم دكتوري", "en": "Qayim Dactory"}',
                'mail_from_email' => '<EMAIL>',
                'no_reply_email' => '<EMAIL>',
                'customer_care_email' => '<EMAIL>',
                'sehatalk_email' => '<EMAIL>',
                'sms_provider' => 'unifonic',
                'sms_host' => 'https://basic.unifonic.com/rest/SMS/messages',
                'sms_auth_key' => 'tMOncp2qcnWKwMgjjtrm7drPFCDzHz',
                'sms_from' => 'ARCOIT',
                'sendgrid_api_key' => '',
                'hyperpay_token' => 'OGFjZGE0Y2I2Yjg4NGYxYjAxNmI4ODZlNDdmMzAyN2J8YTdCVDdqNVNOeg==',
                'hyperpay_entity_id' => '8acda4cb6b884f1b016b886ec9150287',
                'zoho_tax_id' => NULL,
                'vat_tax' => '0.00',
                'zoho_organization_id' => '*********',
                'zoho_client_id' => '1000.ZDITO0PH1QTELBDHNQMTTLPDWIIOTA',
                'zoho_client_secret' => '70390085ff4fc97602d8c4c93229219e9dc4bfec44',
                'zoho_redirect_uri' => 'https://api.qayimdactory.com/api/zoho/redirect_uri',
                'zoho_code' => '**********************************************************************',
                'zoho_access_token' => '**********************************************************************',
                'zoho_refresh_token' => '**********************************************************************',
                'zoho_access_token_expired_at' => '2022-09-01 10:00:07',
                'zoho_scope' => 'ZohoBooks.fullaccess.all,ZohoCRM.modules.ALL',
                'zoho_template_id' => '1745946000000017001',
                'google_analytics_key' => NULL,
                'google_adsense_code_snippets' => NULL,
                'meta_keywords' => '{"ar": null, "en": "{\\"ar\\": null}"}',
                'meta_description' => '{"ar": null, "en": "{\\"ar\\": null}"}',
                'created_at' => '2021-08-19 07:59:30',
                'updated_at' => '2022-12-02 21:32:23',
                'created_by' => NULL,
                'updated_by' => 1,
            ),
        ));
        
        
    }
}