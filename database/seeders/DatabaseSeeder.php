<?php

namespace Database\Seeders;

use App\Models\User;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $user = User::whereJsonContains('name', ['ar' => 'الأدمن', 'en' => 'Admin'])
            ->whereJsonContains('full_name', ['ar' => 'الأدمن', 'en' => 'Admin'])
            ->where('email', '<EMAIL>')
            ->first();

        if (!$user) {
            User::create([
                'name'     => ['ar' => 'الأدمن', 'en' => 'Admin'],
                'full_name' => ['ar' => 'الأدمن', 'en' => 'Admin'],
                'email'    => '<EMAIL>',
                'password' => Hash::make('password'),
            ]);
        }
        $this->call(UsersTableSeeder::class);
        $this->call(CountriesTableSeeder::class);
        $this->call(StatesTableSeeder::class);
        $this->call(CitiesTableSeeder::class);
        $this->call(DepartmentsTableSeeder::class);
        $this->call(SpecialtiesTableSeeder::class);
        $this->call(SubspecialtiesTableSeeder::class);
        $this->call(RanksTableSeeder::class);
        $this->call(SettingsTableSeeder::class);
        $this->call(InstituteTypesTableSeeder::class);
        $this->call(InstitutesTableSeeder::class);
        $this->call(InstituteMessagesTableSeeder::class);
        $this->call(DoctorsTableSeeder::class);
        $this->call(RatingsTableSeeder::class);
        $this->call(KpisTableSeeder::class);
        $this->call(RatingDetailsTableSeeder::class);
    }
}
