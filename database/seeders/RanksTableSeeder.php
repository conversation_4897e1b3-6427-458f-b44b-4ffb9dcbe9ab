<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class RanksTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('ranks')->delete();
        
        \DB::table('ranks')->insert(array (
            0 => 
            array (
                'id' => 1,
                'title' => '{"ar": "استشاري", "en": "Consultant"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'title' => '{"ar": "استشاري اول", "en": "Senior Consultant"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'title' => '{"ar": "اخصائي", "en": "Specialist"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'title' => '{"ar": "اخصائي اول", "en": "Senior Specialist"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'title' => '{"ar": "طبيب مقيم", "en": "Resident"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'title' => '{"ar": "طبيب", "en": "MD"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'title' => '{"ar": "مستشار", "en": "Advisor"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'title' => '{"ar": "بروفيسور", "en": "Profissor"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'title' => '{"ar": "بصريات", "en": "Optics"}',
                'status' => 'active',
                'created_at' => '2021-09-20 20:32:01',
                'updated_at' => '2021-09-20 20:32:01',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        
        
    }
}