<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StatesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('states')->delete();
        
        \DB::table('states')->insert(array (
            0 => 
            array (
                'id' => 1,
                'country_code' => 'SA',
                'state_code' => 'SA-02',
                'title' => '{"ar": "مكة المكرمة", "en": "Makkah"}',
                'physical' => 'West',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'country_code' => 'SA',
                'state_code' => 'SA-03',
                'title' => '{"ar": "المدينة المنورة", "en": "Madinah"}',
                'physical' => 'West',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'country_code' => 'SA',
                'state_code' => 'SA-01',
                'title' => '{"ar": "الرياض", "en": "Riyadh"}',
                'physical' => 'Central',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'country_code' => 'SA',
                'state_code' => 'SA-05',
                'title' => '{"ar": "القصيم", "en": "Qasim"}',
                'physical' => 'Central',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'country_code' => 'SA',
                'state_code' => 'SA-04',
                'title' => '{"ar": "الشرقية", "en": "Eastern"}',
                'physical' => 'East',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'country_code' => 'SA',
                'state_code' => 'SA-14',
                'title' => '{"ar": "عسير", "en": "Asser"}',
                'physical' => 'South',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'country_code' => 'SA',
                'state_code' => 'SA-07',
                'title' => '{"ar": "تبوك", "en": "Tabuk"}',
                'physical' => 'North',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'country_code' => 'SA',
                'state_code' => 'SA-06',
                'title' => '{"ar": "حائل", "en": "Hail"}',
                'physical' => 'North',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'country_code' => 'SA',
                'state_code' => 'SA-08',
                'title' => '{"ar": "الحدود الشمالية", "en": "North Borders"}',
                'physical' => 'North',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'country_code' => 'SA',
                'state_code' => 'SA-09',
                'title' => '{"ar": "جازان", "en": "Jazan"}',
                'physical' => 'South',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'country_code' => 'SA',
                'state_code' => 'SA-10',
                'title' => '{"ar": "نجران", "en": "Najran"}',
                'physical' => 'South',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'country_code' => 'SA',
                'state_code' => 'SA-11',
                'title' => '{"ar": "الباحة", "en": "AlBaha"}',
                'physical' => 'South',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'country_code' => 'SA',
                'state_code' => 'SA-12',
                'title' => '{"ar": "الجوف", "en": "AlJoaf"}',
                'physical' => 'North',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'country_code' => 'SA',
                'state_code' => '017',
                'title' => '{"ar": "بْهَا", "en": "Abha"}',
                'physical' => NULL,
                'status' => 'active',
                'created_at' => '2023-12-20 18:38:16',
                'updated_at' => '2023-12-20 18:47:33',
                'deleted_at' => '2023-12-20 18:47:33',
                'created_by' => 1,
                'updated_by' => NULL,
                'deleted_by' => 1,
            ),
        ));
        
        
    }
}