<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CountriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('countries')->delete();
        
        \DB::table('countries')->insert(array (
            0 => 
            array (
                'id' => 1,
                'code' => 'AF',
                'code_iso' => 'AFG',
                'title' => '{"ar": "Afghanistan", "en": "Afghanistan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kabul", "en": "Kabul"}',
                'dialing_code' => '+93',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'code' => 'AL',
                'code_iso' => 'ALB',
                'title' => '{"ar": "Albania", "en": "Albania"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tirana", "en": "Tirana"}',
                'dialing_code' => '+355',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'code' => 'DZ',
                'code_iso' => 'DZA',
                'title' => '{"ar": "Algeria", "en": "Algeria"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Algiers", "en": "Algiers"}',
                'dialing_code' => '+213',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'code' => 'AS',
                'code_iso' => 'ASM',
                'title' => '{"ar": "American Samoa", "en": "American Samoa"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Pago Pago", "en": "Pago Pago"}',
                'dialing_code' => '+1-684',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'code' => 'AD',
                'code_iso' => 'AND',
                'title' => '{"ar": "Andorra", "en": "Andorra"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Andorra la Vella", "en": "Andorra la Vella"}',
                'dialing_code' => '+376',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'code' => 'AO',
                'code_iso' => 'AGO',
                'title' => '{"ar": "Angola", "en": "Angola"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Luanda", "en": "Luanda"}',
                'dialing_code' => '+244',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'code' => 'AI',
                'code_iso' => 'AIA',
                'title' => '{"ar": "Anguilla", "en": "Anguilla"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "The Valley", "en": "The Valley"}',
                'dialing_code' => '+1-264',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'code' => 'AQ',
                'code_iso' => 'ATA',
                'title' => '{"ar": "Antarctica", "en": "Antarctica"}',
                'flag' => NULL,
                'capital_name' => '{"ar": ""}',
                'dialing_code' => '+672',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'code' => 'AG',
                'code_iso' => 'ATG',
                'title' => '{"ar": "Antigua and Barbuda", "en": "Antigua and Barbuda"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "St. John\'s", "en": "St. John\'s"}',
                'dialing_code' => '+1-268',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'code' => 'AR',
                'code_iso' => 'ARG',
                'title' => '{"ar": "Argentina", "en": "Argentina"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Buenos Aires", "en": "Buenos Aires"}',
                'dialing_code' => '+54',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'code' => 'AM',
                'code_iso' => 'ARM',
                'title' => '{"ar": "Armenia", "en": "Armenia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Yerevan", "en": "Yerevan"}',
                'dialing_code' => '+374',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'code' => 'AW',
                'code_iso' => 'ABW',
                'title' => '{"ar": "Aruba", "en": "Aruba"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Oranjestad", "en": "Oranjestad"}',
                'dialing_code' => '+297',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'code' => 'AU',
                'code_iso' => 'AUS',
                'title' => '{"ar": "Australia", "en": "Australia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Canberra", "en": "Canberra"}',
                'dialing_code' => '+61',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'code' => 'AT',
                'code_iso' => 'AUT',
                'title' => '{"ar": "Austria", "en": "Austria"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Vienna", "en": "Vienna"}',
                'dialing_code' => '+43',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'code' => 'AZ',
                'code_iso' => 'AZE',
                'title' => '{"ar": "Azerbaijan", "en": "Azerbaijan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Baku", "en": "Baku"}',
                'dialing_code' => '+994',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 16,
                'code' => 'BS',
                'code_iso' => 'BHS',
                'title' => '{"ar": "Bahamas", "en": "Bahamas"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nassau", "en": "Nassau"}',
                'dialing_code' => '+1-242',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'code' => 'BH',
                'code_iso' => 'BHR',
                'title' => '{"ar": "Bahrain", "en": "Bahrain"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Manama", "en": "Manama"}',
                'dialing_code' => '+973',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'code' => 'BD',
                'code_iso' => 'BGD',
                'title' => '{"ar": "Bangladesh", "en": "Bangladesh"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dhaka", "en": "Dhaka"}',
                'dialing_code' => '+880',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'code' => 'BB',
                'code_iso' => 'BRB',
                'title' => '{"ar": "Barbados", "en": "Barbados"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bridgetown", "en": "Bridgetown"}',
                'dialing_code' => '+1-246',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'code' => 'BY',
                'code_iso' => 'BLR',
                'title' => '{"ar": "Belarus", "en": "Belarus"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Minsk", "en": "Minsk"}',
                'dialing_code' => '+375',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'code' => 'BE',
                'code_iso' => 'BEL',
                'title' => '{"ar": "Belgium", "en": "Belgium"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Brussels", "en": "Brussels"}',
                'dialing_code' => '+32',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'code' => 'BZ',
                'code_iso' => 'BLZ',
                'title' => '{"ar": "Belize", "en": "Belize"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Belmopan", "en": "Belmopan"}',
                'dialing_code' => '+501',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'code' => 'BJ',
                'code_iso' => 'BEN',
                'title' => '{"ar": "Benin", "en": "Benin"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Porto-Novo", "en": "Porto-Novo"}',
                'dialing_code' => '+229',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'code' => 'BM',
                'code_iso' => 'BMU',
                'title' => '{"ar": "Bermuda", "en": "Bermuda"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Hamilton", "en": "Hamilton"}',
                'dialing_code' => '+1-441',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'code' => 'BT',
                'code_iso' => 'BTN',
                'title' => '{"ar": "Bhutan", "en": "Bhutan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Thimphu", "en": "Thimphu"}',
                'dialing_code' => '+975',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'code' => 'BO',
                'code_iso' => 'BOL',
                'title' => '{"ar": "Bolivia", "en": "Bolivia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Sucre", "en": "Sucre"}',
                'dialing_code' => '+591',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            26 => 
            array (
                'id' => 27,
                'code' => 'BA',
                'code_iso' => 'BIH',
                'title' => '{"ar": "Bosnia and Herzegovina", "en": "Bosnia and Herzegovina"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Sarajevo", "en": "Sarajevo"}',
                'dialing_code' => '+387',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            27 => 
            array (
                'id' => 28,
                'code' => 'BW',
                'code_iso' => 'BWA',
                'title' => '{"ar": "Botswana", "en": "Botswana"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Gaborone", "en": "Gaborone"}',
                'dialing_code' => '+267',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            28 => 
            array (
                'id' => 29,
                'code' => 'BV',
                'code_iso' => 'BVT',
                'title' => '{"ar": "Bouvet Island", "en": "Bouvet Island"}',
                'flag' => NULL,
                'capital_name' => '{"ar": ""}',
                'dialing_code' => '+55',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            29 => 
            array (
                'id' => 30,
                'code' => 'BR',
                'code_iso' => 'BRA',
                'title' => '{"ar": "Brazil", "en": "Brazil"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Brasilia", "en": "Brasilia"}',
                'dialing_code' => '+55',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            30 => 
            array (
                'id' => 31,
                'code' => 'IO',
                'code_iso' => 'IOT',
                'title' => '{"ar": "British Indian Ocean Territory", "en": "British Indian Ocean Territory"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Diego Garcia", "en": "Diego Garcia"}',
                'dialing_code' => '+246',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            31 => 
            array (
                'id' => 32,
                'code' => 'BN',
                'code_iso' => 'BRN',
                'title' => '{"ar": "Brunei", "en": "Brunei"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bandar Seri Begawan", "en": "Bandar Seri Begawan"}',
                'dialing_code' => '+673',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            32 => 
            array (
                'id' => 33,
                'code' => 'BG',
                'code_iso' => 'BGR',
                'title' => '{"ar": "Bulgaria", "en": "Bulgaria"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Sofia", "en": "Sofia"}',
                'dialing_code' => '+359',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            33 => 
            array (
                'id' => 34,
                'code' => 'BF',
                'code_iso' => 'BFA',
                'title' => '{"ar": "Burkina Faso", "en": "Burkina Faso"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ouagadougou", "en": "Ouagadougou"}',
                'dialing_code' => '+226',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            34 => 
            array (
                'id' => 35,
                'code' => 'BI',
                'code_iso' => 'BDI',
                'title' => '{"ar": "Burundi", "en": "Burundi"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bujumbura", "en": "Bujumbura"}',
                'dialing_code' => '+257',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            35 => 
            array (
                'id' => 36,
                'code' => 'KH',
                'code_iso' => 'KHM',
                'title' => '{"ar": "Cambodia", "en": "Cambodia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Phnom Penh", "en": "Phnom Penh"}',
                'dialing_code' => '+855',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            36 => 
            array (
                'id' => 37,
                'code' => 'CM',
                'code_iso' => 'CMR',
                'title' => '{"ar": "Cameroon", "en": "Cameroon"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Yaounde", "en": "Yaounde"}',
                'dialing_code' => '+237',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            37 => 
            array (
                'id' => 38,
                'code' => 'CA',
                'code_iso' => 'CAN',
                'title' => '{"ar": "Canada", "en": "Canada"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ottawa", "en": "Ottawa"}',
                'dialing_code' => '+1',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            38 => 
            array (
                'id' => 39,
                'code' => 'CV',
                'code_iso' => 'CPV',
                'title' => '{"ar": "Cape Verde", "en": "Cape Verde"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Praia", "en": "Praia"}',
                'dialing_code' => '+238',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            39 => 
            array (
                'id' => 40,
                'code' => 'KY',
                'code_iso' => 'CYM',
                'title' => '{"ar": "Cayman Islands", "en": "Cayman Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "George Town", "en": "George Town"}',
                'dialing_code' => '+1-345',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            40 => 
            array (
                'id' => 41,
                'code' => 'CF',
                'code_iso' => 'CAF',
                'title' => '{"ar": "Central African Republic", "en": "Central African Republic"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bangui", "en": "Bangui"}',
                'dialing_code' => '+236',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            41 => 
            array (
                'id' => 42,
                'code' => 'TD',
                'code_iso' => 'TCD',
                'title' => '{"ar": "Chad", "en": "Chad"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "N\'Djamena", "en": "N\'Djamena"}',
                'dialing_code' => '+235',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            42 => 
            array (
                'id' => 43,
                'code' => 'CL',
                'code_iso' => 'CHL',
                'title' => '{"ar": "Chile", "en": "Chile"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Santiago", "en": "Santiago"}',
                'dialing_code' => '+56',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            43 => 
            array (
                'id' => 44,
                'code' => 'CN',
                'code_iso' => 'CHN',
                'title' => '{"ar": "China", "en": "China"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Beijing", "en": "Beijing"}',
                'dialing_code' => '+86',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            44 => 
            array (
                'id' => 45,
                'code' => 'CX',
                'code_iso' => 'CXR',
                'title' => '{"ar": "Christmas Island", "en": "Christmas Island"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Flying Fish Cove", "en": "Flying Fish Cove"}',
                'dialing_code' => '+61',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            45 => 
            array (
                'id' => 46,
                'code' => 'CC',
                'code_iso' => 'CCK',
                'title' => '{"ar": "Cocos Islands", "en": "Cocos Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "West Island", "en": "West Island"}',
                'dialing_code' => '+61',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            46 => 
            array (
                'id' => 47,
                'code' => 'CO',
                'code_iso' => 'COL',
                'title' => '{"ar": "Colombia", "en": "Colombia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bogota", "en": "Bogota"}',
                'dialing_code' => '+57',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            47 => 
            array (
                'id' => 48,
                'code' => 'KM',
                'code_iso' => 'COM',
                'title' => '{"ar": "Comoros", "en": "Comoros"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Moroni", "en": "Moroni"}',
                'dialing_code' => '+269',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            48 => 
            array (
                'id' => 49,
                'code' => 'CG',
                'code_iso' => 'COG',
                'title' => '{"ar": "Republic of the Congo", "en": "Republic of the Congo"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Brazzaville", "en": "Brazzaville"}',
                'dialing_code' => '+242',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'code' => 'CD',
                'code_iso' => 'COD',
                'title' => '{"ar": "Democratic Republic of the Congo", "en": "Democratic Republic of the Congo"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kinshasa", "en": "Kinshasa"}',
                'dialing_code' => '+243',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'code' => 'CK',
                'code_iso' => 'COK',
                'title' => '{"ar": "Cook Islands", "en": "Cook Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Avarua", "en": "Avarua"}',
                'dialing_code' => '+682',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'code' => 'CR',
                'code_iso' => 'CRI',
                'title' => '{"ar": "Costa Rica", "en": "Costa Rica"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "San Jose", "en": "San Jose"}',
                'dialing_code' => '+506',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'code' => 'CI',
                'code_iso' => 'CIV',
                'title' => '{"ar": "Ivory Coast", "en": "Ivory Coast"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Yamoussoukro", "en": "Yamoussoukro"}',
                'dialing_code' => '+225',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            53 => 
            array (
                'id' => 54,
                'code' => 'HR',
                'code_iso' => 'HRV',
                'title' => '{"ar": "Croatia", "en": "Croatia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Zagreb", "en": "Zagreb"}',
                'dialing_code' => '+385',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            54 => 
            array (
                'id' => 55,
                'code' => 'CU',
                'code_iso' => 'CUB',
                'title' => '{"ar": "Cuba", "en": "Cuba"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Havana", "en": "Havana"}',
                'dialing_code' => '+53',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            55 => 
            array (
                'id' => 56,
                'code' => 'CY',
                'code_iso' => 'CYP',
                'title' => '{"ar": "Cyprus", "en": "Cyprus"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nicosia", "en": "Nicosia"}',
                'dialing_code' => '+357',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            56 => 
            array (
                'id' => 57,
                'code' => 'CZ',
                'code_iso' => 'CZE',
                'title' => '{"ar": "Czech Republic", "en": "Czech Republic"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Prague", "en": "Prague"}',
                'dialing_code' => '+420',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            57 => 
            array (
                'id' => 58,
                'code' => 'DK',
                'code_iso' => 'DNK',
                'title' => '{"ar": "Denmark", "en": "Denmark"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Copenhagen", "en": "Copenhagen"}',
                'dialing_code' => '+45',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            58 => 
            array (
                'id' => 59,
                'code' => 'DJ',
                'code_iso' => 'DJI',
                'title' => '{"ar": "Djibouti", "en": "Djibouti"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Djibouti", "en": "Djibouti"}',
                'dialing_code' => '+253',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            59 => 
            array (
                'id' => 60,
                'code' => 'DM',
                'code_iso' => 'DMA',
                'title' => '{"ar": "Dominica", "en": "Dominica"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Roseau", "en": "Roseau"}',
                'dialing_code' => '+1-767',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            60 => 
            array (
                'id' => 61,
                'code' => 'DO',
                'code_iso' => 'DOM',
                'title' => '{"ar": "Dominican Republic", "en": "Dominican Republic"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Santo Domingo", "en": "Santo Domingo"}',
                'dialing_code' => '+1',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            61 => 
            array (
                'id' => 62,
                'code' => 'TL',
                'code_iso' => 'TLS',
                'title' => '{"ar": "East Timor", "en": "East Timor"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dili", "en": "Dili"}',
                'dialing_code' => '+670',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            62 => 
            array (
                'id' => 63,
                'code' => 'EC',
                'code_iso' => 'ECU',
                'title' => '{"ar": "Ecuador", "en": "Ecuador"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Quito", "en": "Quito"}',
                'dialing_code' => '+593',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            63 => 
            array (
                'id' => 64,
                'code' => 'EG',
                'code_iso' => 'EGY',
                'title' => '{"ar": "Egypt", "en": "Egypt"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Cairo", "en": "Cairo"}',
                'dialing_code' => '+20',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:57',
                'updated_at' => '2021-09-20 20:31:57',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            64 => 
            array (
                'id' => 65,
                'code' => 'SV',
                'code_iso' => 'SLV',
                'title' => '{"ar": "El Salvador", "en": "El Salvador"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "San Salvador", "en": "San Salvador"}',
                'dialing_code' => '+503',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            65 => 
            array (
                'id' => 66,
                'code' => 'GQ',
                'code_iso' => 'GNQ',
                'title' => '{"ar": "Equatorial Guinea", "en": "Equatorial Guinea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Malabo", "en": "Malabo"}',
                'dialing_code' => '+240',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            66 => 
            array (
                'id' => 67,
                'code' => 'ER',
                'code_iso' => 'ERI',
                'title' => '{"ar": "Eritrea", "en": "Eritrea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Asmara", "en": "Asmara"}',
                'dialing_code' => '+291',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            67 => 
            array (
                'id' => 68,
                'code' => 'EE',
                'code_iso' => 'EST',
                'title' => '{"ar": "Estonia", "en": "Estonia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tallinn", "en": "Tallinn"}',
                'dialing_code' => '+372',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            68 => 
            array (
                'id' => 69,
                'code' => 'ET',
                'code_iso' => 'ETH',
                'title' => '{"ar": "Ethiopia", "en": "Ethiopia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Addis Ababa", "en": "Addis Ababa"}',
                'dialing_code' => '+251',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            69 => 
            array (
                'id' => 70,
                'code' => 'FK',
                'code_iso' => 'FLK',
                'title' => '{"ar": "Falkland Islands", "en": "Falkland Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Stanley", "en": "Stanley"}',
                'dialing_code' => '+500',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            70 => 
            array (
                'id' => 71,
                'code' => 'FO',
                'code_iso' => 'FRO',
                'title' => '{"ar": "Faroe Islands", "en": "Faroe Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Torshavn", "en": "Torshavn"}',
                'dialing_code' => '+298',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            71 => 
            array (
                'id' => 72,
                'code' => 'FJ',
                'code_iso' => 'FJI',
                'title' => '{"ar": "Fiji", "en": "Fiji"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Suva", "en": "Suva"}',
                'dialing_code' => '+679',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            72 => 
            array (
                'id' => 73,
                'code' => 'FI',
                'code_iso' => 'FIN',
                'title' => '{"ar": "Finland", "en": "Finland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Helsinki", "en": "Helsinki"}',
                'dialing_code' => '+358',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            73 => 
            array (
                'id' => 74,
                'code' => 'FR',
                'code_iso' => 'FRA',
                'title' => '{"ar": "France", "en": "France"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Paris", "en": "Paris"}',
                'dialing_code' => '+33',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            74 => 
            array (
                'id' => 75,
                'code' => 'GF',
                'code_iso' => 'GUF',
                'title' => '{"ar": "French Guiana", "en": "French Guiana"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Cayenne", "en": "Cayenne"}',
                'dialing_code' => '+594',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            75 => 
            array (
                'id' => 76,
                'code' => 'PF',
                'code_iso' => 'PYF',
                'title' => '{"ar": "French Polynesia", "en": "French Polynesia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Papeete", "en": "Papeete"}',
                'dialing_code' => '+689',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            76 => 
            array (
                'id' => 77,
                'code' => 'TF',
                'code_iso' => 'ATF',
                'title' => '{"ar": "French Southern Territories", "en": "French Southern Territories"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port-aux-Francais", "en": "Port-aux-Francais"}',
                'dialing_code' => '+262',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            77 => 
            array (
                'id' => 78,
                'code' => 'GA',
                'code_iso' => 'GAB',
                'title' => '{"ar": "Gabon", "en": "Gabon"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Libreville", "en": "Libreville"}',
                'dialing_code' => '+241',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            78 => 
            array (
                'id' => 79,
                'code' => 'GM',
                'code_iso' => 'GMB',
                'title' => '{"ar": "Gambia", "en": "Gambia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Banjul", "en": "Banjul"}',
                'dialing_code' => '+220',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            79 => 
            array (
                'id' => 80,
                'code' => 'GE',
                'code_iso' => 'GEO',
                'title' => '{"ar": "Georgia", "en": "Georgia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tbilisi", "en": "Tbilisi"}',
                'dialing_code' => '+995',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            80 => 
            array (
                'id' => 81,
                'code' => 'DE',
                'code_iso' => 'DEU',
                'title' => '{"ar": "Germany", "en": "Germany"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Berlin", "en": "Berlin"}',
                'dialing_code' => '+49',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            81 => 
            array (
                'id' => 82,
                'code' => 'GH',
                'code_iso' => 'GHA',
                'title' => '{"ar": "Ghana", "en": "Ghana"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Accra", "en": "Accra"}',
                'dialing_code' => '+233',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            82 => 
            array (
                'id' => 83,
                'code' => 'GI',
                'code_iso' => 'GIB',
                'title' => '{"ar": "Gibraltar", "en": "Gibraltar"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Gibraltar", "en": "Gibraltar"}',
                'dialing_code' => '+350',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            83 => 
            array (
                'id' => 84,
                'code' => 'GR',
                'code_iso' => 'GRC',
                'title' => '{"ar": "Greece", "en": "Greece"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Athens", "en": "Athens"}',
                'dialing_code' => '+30',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            84 => 
            array (
                'id' => 85,
                'code' => 'GL',
                'code_iso' => 'GRL',
                'title' => '{"ar": "Greenland", "en": "Greenland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nuuk", "en": "Nuuk"}',
                'dialing_code' => '+299',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            85 => 
            array (
                'id' => 86,
                'code' => 'GD',
                'code_iso' => 'GRD',
                'title' => '{"ar": "Grenada", "en": "Grenada"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "St. George\'s", "en": "St. George\'s"}',
                'dialing_code' => '+1-473',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            86 => 
            array (
                'id' => 87,
                'code' => 'GP',
                'code_iso' => 'GLP',
                'title' => '{"ar": "Guadeloupe", "en": "Guadeloupe"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Basse-Terre", "en": "Basse-Terre"}',
                'dialing_code' => '+590',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            87 => 
            array (
                'id' => 88,
                'code' => 'GU',
                'code_iso' => 'GUM',
                'title' => '{"ar": "Guam", "en": "Guam"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Hagatna", "en": "Hagatna"}',
                'dialing_code' => '+1-671',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            88 => 
            array (
                'id' => 89,
                'code' => 'GT',
                'code_iso' => 'GTM',
                'title' => '{"ar": "Guatemala", "en": "Guatemala"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Guatemala City", "en": "Guatemala City"}',
                'dialing_code' => '+502',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            89 => 
            array (
                'id' => 90,
                'code' => 'GG',
                'code_iso' => 'GGY',
                'title' => '{"ar": "Guernsey", "en": "Guernsey"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "St Peter Port", "en": "St Peter Port"}',
                'dialing_code' => '+44-1481',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            90 => 
            array (
                'id' => 91,
                'code' => 'GN',
                'code_iso' => 'GIN',
                'title' => '{"ar": "Guinea", "en": "Guinea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Conakry", "en": "Conakry"}',
                'dialing_code' => '+224',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            91 => 
            array (
                'id' => 92,
                'code' => 'GW',
                'code_iso' => 'GNB',
                'title' => '{"ar": "Guinea-Bissau", "en": "Guinea-Bissau"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bissau", "en": "Bissau"}',
                'dialing_code' => '+245',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            92 => 
            array (
                'id' => 93,
                'code' => 'GY',
                'code_iso' => 'GUY',
                'title' => '{"ar": "Guyana", "en": "Guyana"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Georgetown", "en": "Georgetown"}',
                'dialing_code' => '+592',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            93 => 
            array (
                'id' => 94,
                'code' => 'HT',
                'code_iso' => 'HTI',
                'title' => '{"ar": "Haiti", "en": "Haiti"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port-au-Prince", "en": "Port-au-Prince"}',
                'dialing_code' => '+509',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            94 => 
            array (
                'id' => 95,
                'code' => 'HM',
                'code_iso' => 'HMD',
                'title' => '{"ar": "Heard Island and McDonald Islands", "en": "Heard Island and McDonald Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": ""}',
                'dialing_code' => '+672',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            95 => 
            array (
                'id' => 96,
                'code' => 'HN',
                'code_iso' => 'HND',
                'title' => '{"ar": "Honduras", "en": "Honduras"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tegucigalpa", "en": "Tegucigalpa"}',
                'dialing_code' => '+504',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            96 => 
            array (
                'id' => 97,
                'code' => 'HK',
                'code_iso' => 'HKG',
                'title' => '{"ar": "Hong Kong", "en": "Hong Kong"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Hong Kong", "en": "Hong Kong"}',
                'dialing_code' => '+852',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            97 => 
            array (
                'id' => 98,
                'code' => 'HU',
                'code_iso' => 'HUN',
                'title' => '{"ar": "Hungary", "en": "Hungary"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Budapest", "en": "Budapest"}',
                'dialing_code' => '+36',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            98 => 
            array (
                'id' => 99,
                'code' => 'IS',
                'code_iso' => 'ISL',
                'title' => '{"ar": "Iceland", "en": "Iceland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Reykjavik", "en": "Reykjavik"}',
                'dialing_code' => '+354',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            99 => 
            array (
                'id' => 100,
                'code' => 'IN',
                'code_iso' => 'IND',
                'title' => '{"ar": "India", "en": "India"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "New Delhi", "en": "New Delhi"}',
                'dialing_code' => '+91',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        \DB::table('countries')->insert(array (
            0 => 
            array (
                'id' => 101,
                'code' => 'ID',
                'code_iso' => 'IDN',
                'title' => '{"ar": "Indonesia", "en": "Indonesia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Jakarta", "en": "Jakarta"}',
                'dialing_code' => '+62',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 102,
                'code' => 'IR',
                'code_iso' => 'IRN',
                'title' => '{"ar": "Iran", "en": "Iran"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tehran", "en": "Tehran"}',
                'dialing_code' => '+98',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 103,
                'code' => 'IQ',
                'code_iso' => 'IRQ',
                'title' => '{"ar": "Iraq", "en": "Iraq"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Baghdad", "en": "Baghdad"}',
                'dialing_code' => '+964',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 104,
                'code' => 'IE',
                'code_iso' => 'IRL',
                'title' => '{"ar": "Ireland", "en": "Ireland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dublin", "en": "Dublin"}',
                'dialing_code' => '+353',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 105,
                'code' => 'IL',
                'code_iso' => 'ISR',
                'title' => '{"ar": "Israel", "en": "Israel"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Jerusalem", "en": "Jerusalem"}',
                'dialing_code' => '+972',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 106,
                'code' => 'IT',
                'code_iso' => 'ITA',
                'title' => '{"ar": "Italy", "en": "Italy"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Rome", "en": "Rome"}',
                'dialing_code' => '+39',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 107,
                'code' => 'JM',
                'code_iso' => 'JAM',
                'title' => '{"ar": "Jamaica", "en": "Jamaica"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kingston", "en": "Kingston"}',
                'dialing_code' => '+1-876',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 108,
                'code' => 'JP',
                'code_iso' => 'JPN',
                'title' => '{"ar": "Japan", "en": "Japan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tokyo", "en": "Tokyo"}',
                'dialing_code' => '+81',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 109,
                'code' => 'JE',
                'code_iso' => 'JEY',
                'title' => '{"ar": "Jersey", "en": "Jersey"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Saint Helier", "en": "Saint Helier"}',
                'dialing_code' => '+44-1534',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 110,
                'code' => 'JO',
                'code_iso' => 'JOR',
                'title' => '{"ar": "Jordan", "en": "Jordan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Amman", "en": "Amman"}',
                'dialing_code' => '+962',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 111,
                'code' => 'KZ',
                'code_iso' => 'KAZ',
                'title' => '{"ar": "Kazakhstan", "en": "Kazakhstan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Astana", "en": "Astana"}',
                'dialing_code' => '+7',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 112,
                'code' => 'KE',
                'code_iso' => 'KEN',
                'title' => '{"ar": "Kenya", "en": "Kenya"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nairobi", "en": "Nairobi"}',
                'dialing_code' => '+254',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 113,
                'code' => 'KI',
                'code_iso' => 'KIR',
                'title' => '{"ar": "Kiribati", "en": "Kiribati"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tarawa", "en": "Tarawa"}',
                'dialing_code' => '+686',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 114,
                'code' => 'KP',
                'code_iso' => 'PRK',
                'title' => '{"ar": "North Korea", "en": "North Korea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Pyongyang", "en": "Pyongyang"}',
                'dialing_code' => '+850',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 115,
                'code' => 'KR',
                'code_iso' => 'KOR',
                'title' => '{"ar": "South Korea", "en": "South Korea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Seoul", "en": "Seoul"}',
                'dialing_code' => '+82',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 116,
                'code' => 'KW',
                'code_iso' => 'KWT',
                'title' => '{"ar": "Kuwait", "en": "Kuwait"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kuwait City", "en": "Kuwait City"}',
                'dialing_code' => '+965',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 117,
                'code' => 'KG',
                'code_iso' => 'KGZ',
                'title' => '{"ar": "Kyrgyzstan", "en": "Kyrgyzstan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bishkek", "en": "Bishkek"}',
                'dialing_code' => '+996',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 118,
                'code' => 'LA',
                'code_iso' => 'LAO',
                'title' => '{"ar": "Laos", "en": "Laos"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Vientiane", "en": "Vientiane"}',
                'dialing_code' => '+856',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 119,
                'code' => 'LV',
                'code_iso' => 'LVA',
                'title' => '{"ar": "Latvia", "en": "Latvia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Riga", "en": "Riga"}',
                'dialing_code' => '+371',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 120,
                'code' => 'LB',
                'code_iso' => 'LBN',
                'title' => '{"ar": "Lebanon", "en": "Lebanon"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Beirut", "en": "Beirut"}',
                'dialing_code' => '+961',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 121,
                'code' => 'LS',
                'code_iso' => 'LSO',
                'title' => '{"ar": "Lesotho", "en": "Lesotho"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Maseru", "en": "Maseru"}',
                'dialing_code' => '+266',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 122,
                'code' => 'LR',
                'code_iso' => 'LBR',
                'title' => '{"ar": "Liberia", "en": "Liberia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Monrovia", "en": "Monrovia"}',
                'dialing_code' => '+231',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 123,
                'code' => 'LY',
                'code_iso' => 'LBY',
                'title' => '{"ar": "Libya", "en": "Libya"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tripolis", "en": "Tripolis"}',
                'dialing_code' => '+218',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 124,
                'code' => 'LI',
                'code_iso' => 'LIE',
                'title' => '{"ar": "Liechtenstein", "en": "Liechtenstein"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Vaduz", "en": "Vaduz"}',
                'dialing_code' => '+423',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 125,
                'code' => 'LT',
                'code_iso' => 'LTU',
                'title' => '{"ar": "Lithuania", "en": "Lithuania"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Vilnius", "en": "Vilnius"}',
                'dialing_code' => '+370',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            25 => 
            array (
                'id' => 126,
                'code' => 'LU',
                'code_iso' => 'LUX',
                'title' => '{"ar": "Luxembourg", "en": "Luxembourg"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Luxembourg", "en": "Luxembourg"}',
                'dialing_code' => '+352',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            26 => 
            array (
                'id' => 127,
                'code' => 'MO',
                'code_iso' => 'MAC',
                'title' => '{"ar": "Macao", "en": "Macao"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Macao", "en": "Macao"}',
                'dialing_code' => '+853',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            27 => 
            array (
                'id' => 128,
                'code' => 'MK',
                'code_iso' => 'MKD',
                'title' => '{"ar": "Macedonia", "en": "Macedonia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Skopje", "en": "Skopje"}',
                'dialing_code' => '+389',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            28 => 
            array (
                'id' => 129,
                'code' => 'MG',
                'code_iso' => 'MDG',
                'title' => '{"ar": "Madagascar", "en": "Madagascar"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Antananarivo", "en": "Antananarivo"}',
                'dialing_code' => '+261',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            29 => 
            array (
                'id' => 130,
                'code' => 'MW',
                'code_iso' => 'MWI',
                'title' => '{"ar": "Malawi", "en": "Malawi"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Lilongwe", "en": "Lilongwe"}',
                'dialing_code' => '+265',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            30 => 
            array (
                'id' => 131,
                'code' => 'MY',
                'code_iso' => 'MYS',
                'title' => '{"ar": "Malaysia", "en": "Malaysia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kuala Lumpur", "en": "Kuala Lumpur"}',
                'dialing_code' => '+60',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            31 => 
            array (
                'id' => 132,
                'code' => 'MV',
                'code_iso' => 'MDV',
                'title' => '{"ar": "Maldives", "en": "Maldives"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Male", "en": "Male"}',
                'dialing_code' => '+960',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            32 => 
            array (
                'id' => 133,
                'code' => 'ML',
                'code_iso' => 'MLI',
                'title' => '{"ar": "Mali", "en": "Mali"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bamako", "en": "Bamako"}',
                'dialing_code' => '+223',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            33 => 
            array (
                'id' => 134,
                'code' => 'MT',
                'code_iso' => 'MLT',
                'title' => '{"ar": "Malta", "en": "Malta"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Valletta", "en": "Valletta"}',
                'dialing_code' => '+356',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            34 => 
            array (
                'id' => 135,
                'code' => 'IM',
                'code_iso' => 'IMN',
                'title' => '{"ar": "Isle of Man", "en": "Isle of Man"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Douglas, Isle of Man", "en": "Douglas, Isle of Man"}',
                'dialing_code' => '+44-1624',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            35 => 
            array (
                'id' => 136,
                'code' => 'MH',
                'code_iso' => 'MHL',
                'title' => '{"ar": "Marshall Islands", "en": "Marshall Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Majuro", "en": "Majuro"}',
                'dialing_code' => '+692',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            36 => 
            array (
                'id' => 137,
                'code' => 'MQ',
                'code_iso' => 'MTQ',
                'title' => '{"ar": "Martinique", "en": "Martinique"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Fort-de-France", "en": "Fort-de-France"}',
                'dialing_code' => '+596',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            37 => 
            array (
                'id' => 138,
                'code' => 'MR',
                'code_iso' => 'MRT',
                'title' => '{"ar": "Mauritania", "en": "Mauritania"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nouakchott", "en": "Nouakchott"}',
                'dialing_code' => '+222',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            38 => 
            array (
                'id' => 139,
                'code' => 'MU',
                'code_iso' => 'MUS',
                'title' => '{"ar": "Mauritius", "en": "Mauritius"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port Louis", "en": "Port Louis"}',
                'dialing_code' => '+230',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            39 => 
            array (
                'id' => 140,
                'code' => 'YT',
                'code_iso' => 'MYT',
                'title' => '{"ar": "Mayotte", "en": "Mayotte"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Mamoudzou", "en": "Mamoudzou"}',
                'dialing_code' => '+262',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            40 => 
            array (
                'id' => 141,
                'code' => 'MX',
                'code_iso' => 'MEX',
                'title' => '{"ar": "Mexico", "en": "Mexico"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Mexico City", "en": "Mexico City"}',
                'dialing_code' => '+52',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            41 => 
            array (
                'id' => 142,
                'code' => 'FM',
                'code_iso' => 'FSM',
                'title' => '{"ar": "Micronesia", "en": "Micronesia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Palikir", "en": "Palikir"}',
                'dialing_code' => '+691',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            42 => 
            array (
                'id' => 143,
                'code' => 'MD',
                'code_iso' => 'MDA',
                'title' => '{"ar": "Moldova", "en": "Moldova"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Chisinau", "en": "Chisinau"}',
                'dialing_code' => '+373',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            43 => 
            array (
                'id' => 144,
                'code' => 'MC',
                'code_iso' => 'MCO',
                'title' => '{"ar": "Monaco", "en": "Monaco"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Monaco", "en": "Monaco"}',
                'dialing_code' => '+377',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            44 => 
            array (
                'id' => 145,
                'code' => 'MN',
                'code_iso' => 'MNG',
                'title' => '{"ar": "Mongolia", "en": "Mongolia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ulan Bator", "en": "Ulan Bator"}',
                'dialing_code' => '+976',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            45 => 
            array (
                'id' => 146,
                'code' => 'MS',
                'code_iso' => 'MSR',
                'title' => '{"ar": "Montserrat", "en": "Montserrat"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Plymouth", "en": "Plymouth"}',
                'dialing_code' => '+1-664',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            46 => 
            array (
                'id' => 147,
                'code' => 'MA',
                'code_iso' => 'MAR',
                'title' => '{"ar": "Morocco", "en": "Morocco"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Rabat", "en": "Rabat"}',
                'dialing_code' => '+212',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            47 => 
            array (
                'id' => 148,
                'code' => 'MZ',
                'code_iso' => 'MOZ',
                'title' => '{"ar": "Mozambique", "en": "Mozambique"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Maputo", "en": "Maputo"}',
                'dialing_code' => '+258',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            48 => 
            array (
                'id' => 149,
                'code' => 'MM',
                'code_iso' => 'MMR',
                'title' => '{"ar": "Myanmar", "en": "Myanmar"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nay Pyi Taw", "en": "Nay Pyi Taw"}',
                'dialing_code' => '+95',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            49 => 
            array (
                'id' => 150,
                'code' => 'NA',
                'code_iso' => 'NAM',
                'title' => '{"ar": "Namibia", "en": "Namibia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Windhoek", "en": "Windhoek"}',
                'dialing_code' => '+264',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            50 => 
            array (
                'id' => 151,
                'code' => 'NR',
                'code_iso' => 'NRU',
                'title' => '{"ar": "Nauru", "en": "Nauru"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Yaren", "en": "Yaren"}',
                'dialing_code' => '+674',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            51 => 
            array (
                'id' => 152,
                'code' => 'NP',
                'code_iso' => 'NPL',
                'title' => '{"ar": "Nepal", "en": "Nepal"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kathmandu", "en": "Kathmandu"}',
                'dialing_code' => '+977',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            52 => 
            array (
                'id' => 153,
                'code' => 'AN',
                'code_iso' => 'ANT',
                'title' => '{"ar": "Netherlands Antilles", "en": "Netherlands Antilles"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Willemstad", "en": "Willemstad"}',
                'dialing_code' => '+599',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            53 => 
            array (
                'id' => 154,
                'code' => 'NL',
                'code_iso' => 'NLD',
                'title' => '{"ar": "Netherlands", "en": "Netherlands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Amsterdam", "en": "Amsterdam"}',
                'dialing_code' => '+31',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            54 => 
            array (
                'id' => 155,
                'code' => 'NC',
                'code_iso' => 'NCL',
                'title' => '{"ar": "New Caledonia", "en": "New Caledonia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Noumea", "en": "Noumea"}',
                'dialing_code' => '+687',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            55 => 
            array (
                'id' => 156,
                'code' => 'NZ',
                'code_iso' => 'NZL',
                'title' => '{"ar": "New Zealand", "en": "New Zealand"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Wellington", "en": "Wellington"}',
                'dialing_code' => '+64',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            56 => 
            array (
                'id' => 157,
                'code' => 'NI',
                'code_iso' => 'NIC',
                'title' => '{"ar": "Nicaragua", "en": "Nicaragua"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Managua", "en": "Managua"}',
                'dialing_code' => '+505',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            57 => 
            array (
                'id' => 158,
                'code' => 'NE',
                'code_iso' => 'NER',
                'title' => '{"ar": "Niger", "en": "Niger"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Niamey", "en": "Niamey"}',
                'dialing_code' => '+227',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            58 => 
            array (
                'id' => 159,
                'code' => 'NG',
                'code_iso' => 'NGA',
                'title' => '{"ar": "Nigeria", "en": "Nigeria"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Abuja", "en": "Abuja"}',
                'dialing_code' => '+234',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            59 => 
            array (
                'id' => 160,
                'code' => 'NU',
                'code_iso' => 'NIU',
                'title' => '{"ar": "Niue", "en": "Niue"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Alofi", "en": "Alofi"}',
                'dialing_code' => '+683',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            60 => 
            array (
                'id' => 161,
                'code' => 'NF',
                'code_iso' => 'NFK',
                'title' => '{"ar": "Norfolk Island", "en": "Norfolk Island"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kingston", "en": "Kingston"}',
                'dialing_code' => '+672',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            61 => 
            array (
                'id' => 162,
                'code' => 'MP',
                'code_iso' => 'MNP',
                'title' => '{"ar": "Northern Mariana Islands", "en": "Northern Mariana Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Saipan", "en": "Saipan"}',
                'dialing_code' => '+1-670',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            62 => 
            array (
                'id' => 163,
                'code' => 'NO',
                'code_iso' => 'NOR',
                'title' => '{"ar": "Norway", "en": "Norway"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Oslo", "en": "Oslo"}',
                'dialing_code' => '+47',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            63 => 
            array (
                'id' => 164,
                'code' => 'OM',
                'code_iso' => 'OMN',
                'title' => '{"ar": "Oman", "en": "Oman"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Muscat", "en": "Muscat"}',
                'dialing_code' => '+968',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            64 => 
            array (
                'id' => 165,
                'code' => 'PK',
                'code_iso' => 'PAK',
                'title' => '{"ar": "Pakistan", "en": "Pakistan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Islamabad", "en": "Islamabad"}',
                'dialing_code' => '+92',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            65 => 
            array (
                'id' => 166,
                'code' => 'PW',
                'code_iso' => 'PLW',
                'title' => '{"ar": "Palau", "en": "Palau"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Melekeok", "en": "Melekeok"}',
                'dialing_code' => '+680',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            66 => 
            array (
                'id' => 167,
                'code' => 'PS',
                'code_iso' => 'PSE',
                'title' => '{"ar": "Palestinian Territory", "en": "Palestinian Territory"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "East Jerusalem", "en": "East Jerusalem"}',
                'dialing_code' => '+970',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            67 => 
            array (
                'id' => 168,
                'code' => 'PA',
                'code_iso' => 'PAN',
                'title' => '{"ar": "Panama", "en": "Panama"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Panama City", "en": "Panama City"}',
                'dialing_code' => '+507',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            68 => 
            array (
                'id' => 169,
                'code' => 'PG',
                'code_iso' => 'PNG',
                'title' => '{"ar": "Papua New Guinea", "en": "Papua New Guinea"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port Moresby", "en": "Port Moresby"}',
                'dialing_code' => '+675',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            69 => 
            array (
                'id' => 170,
                'code' => 'PY',
                'code_iso' => 'PRY',
                'title' => '{"ar": "Paraguay", "en": "Paraguay"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Asuncion", "en": "Asuncion"}',
                'dialing_code' => '+595',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            70 => 
            array (
                'id' => 171,
                'code' => 'PE',
                'code_iso' => 'PER',
                'title' => '{"ar": "Peru", "en": "Peru"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Lima", "en": "Lima"}',
                'dialing_code' => '+51',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            71 => 
            array (
                'id' => 172,
                'code' => 'PH',
                'code_iso' => 'PHL',
                'title' => '{"ar": "Philippines", "en": "Philippines"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Manila", "en": "Manila"}',
                'dialing_code' => '+63',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            72 => 
            array (
                'id' => 173,
                'code' => 'PN',
                'code_iso' => 'PCN',
                'title' => '{"ar": "Pitcairn", "en": "Pitcairn"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Adamstown", "en": "Adamstown"}',
                'dialing_code' => '+870',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            73 => 
            array (
                'id' => 174,
                'code' => 'PL',
                'code_iso' => 'POL',
                'title' => '{"ar": "Poland", "en": "Poland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Warsaw", "en": "Warsaw"}',
                'dialing_code' => '+48',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            74 => 
            array (
                'id' => 175,
                'code' => 'PT',
                'code_iso' => 'PRT',
                'title' => '{"ar": "Portugal", "en": "Portugal"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Lisbon", "en": "Lisbon"}',
                'dialing_code' => '+351',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            75 => 
            array (
                'id' => 176,
                'code' => 'PR',
                'code_iso' => 'PRI',
                'title' => '{"ar": "Puerto Rico", "en": "Puerto Rico"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "San Juan", "en": "San Juan"}',
                'dialing_code' => '+1',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            76 => 
            array (
                'id' => 177,
                'code' => 'QA',
                'code_iso' => 'QAT',
                'title' => '{"ar": "Qatar", "en": "Qatar"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Doha", "en": "Doha"}',
                'dialing_code' => '+974',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            77 => 
            array (
                'id' => 178,
                'code' => 'RE',
                'code_iso' => 'REU',
                'title' => '{"ar": "Reunion", "en": "Reunion"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Saint-Denis", "en": "Saint-Denis"}',
                'dialing_code' => '+262',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            78 => 
            array (
                'id' => 179,
                'code' => 'RO',
                'code_iso' => 'ROU',
                'title' => '{"ar": "Romania", "en": "Romania"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bucharest", "en": "Bucharest"}',
                'dialing_code' => '+40',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            79 => 
            array (
                'id' => 180,
                'code' => 'RU',
                'code_iso' => 'RUS',
                'title' => '{"ar": "Russia", "en": "Russia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Moscow", "en": "Moscow"}',
                'dialing_code' => '+7',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            80 => 
            array (
                'id' => 181,
                'code' => 'RW',
                'code_iso' => 'RWA',
                'title' => '{"ar": "Rwanda", "en": "Rwanda"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kigali", "en": "Kigali"}',
                'dialing_code' => '+250',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            81 => 
            array (
                'id' => 182,
                'code' => 'SH',
                'code_iso' => 'SHN',
                'title' => '{"ar": "Saint Helena", "en": "Saint Helena"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Jamestown", "en": "Jamestown"}',
                'dialing_code' => '+290',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            82 => 
            array (
                'id' => 183,
                'code' => 'KN',
                'code_iso' => 'KNA',
                'title' => '{"ar": "Saint Kitts and Nevis", "en": "Saint Kitts and Nevis"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Basseterre", "en": "Basseterre"}',
                'dialing_code' => '+1-869',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            83 => 
            array (
                'id' => 184,
                'code' => 'LC',
                'code_iso' => 'LCA',
                'title' => '{"ar": "Saint Lucia", "en": "Saint Lucia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Castries", "en": "Castries"}',
                'dialing_code' => '+1-758',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            84 => 
            array (
                'id' => 185,
                'code' => 'PM',
                'code_iso' => 'SPM',
                'title' => '{"ar": "Saint Pierre and Miquelon", "en": "Saint Pierre and Miquelon"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Saint-Pierre", "en": "Saint-Pierre"}',
                'dialing_code' => '+508',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            85 => 
            array (
                'id' => 186,
                'code' => 'VC',
                'code_iso' => 'VCT',
                'title' => '{"ar": "Saint Vincent and the Grenadines", "en": "Saint Vincent and the Grenadines"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kingstown", "en": "Kingstown"}',
                'dialing_code' => '+1-784',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            86 => 
            array (
                'id' => 187,
                'code' => 'WS',
                'code_iso' => 'WSM',
                'title' => '{"ar": "Samoa", "en": "Samoa"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Apia", "en": "Apia"}',
                'dialing_code' => '+685',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:58',
                'updated_at' => '2021-09-20 20:31:58',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            87 => 
            array (
                'id' => 188,
                'code' => 'SM',
                'code_iso' => 'SMR',
                'title' => '{"ar": "San Marino", "en": "San Marino"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "San Marino", "en": "San Marino"}',
                'dialing_code' => '+378',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            88 => 
            array (
                'id' => 189,
                'code' => 'ST',
                'code_iso' => 'STP',
                'title' => '{"ar": "Sao Tome and Principe", "en": "Sao Tome and Principe"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Sao Tome", "en": "Sao Tome"}',
                'dialing_code' => '+239',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            89 => 
            array (
                'id' => 190,
                'code' => 'SA',
                'code_iso' => 'SAU',
                'title' => '{"ar": "المملكة العربية السعودية", "en": "Saudi Arabia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "الرياض", "en": "Riyadh"}',
                'dialing_code' => '+966',
                'status' => 'active',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => NULL,
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            90 => 
            array (
                'id' => 191,
                'code' => 'SN',
                'code_iso' => 'SEN',
                'title' => '{"ar": "Senegal", "en": "Senegal"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dakar", "en": "Dakar"}',
                'dialing_code' => '+221',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            91 => 
            array (
                'id' => 192,
                'code' => 'RS',
                'code_iso' => 'SRB',
                'title' => '{"ar": "Serbia", "en": "Serbia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Belgrade", "en": "Belgrade"}',
                'dialing_code' => '+381',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            92 => 
            array (
                'id' => 193,
                'code' => 'SC',
                'code_iso' => 'SYC',
                'title' => '{"ar": "Seychelles", "en": "Seychelles"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Victoria", "en": "Victoria"}',
                'dialing_code' => '+248',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            93 => 
            array (
                'id' => 194,
                'code' => 'SL',
                'code_iso' => 'SLE',
                'title' => '{"ar": "Sierra Leone", "en": "Sierra Leone"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Freetown", "en": "Freetown"}',
                'dialing_code' => '+232',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            94 => 
            array (
                'id' => 195,
                'code' => 'SG',
                'code_iso' => 'SGP',
                'title' => '{"ar": "Singapore", "en": "Singapore"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Singapur", "en": "Singapur"}',
                'dialing_code' => '+65',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            95 => 
            array (
                'id' => 196,
                'code' => 'SK',
                'code_iso' => 'SVK',
                'title' => '{"ar": "Slovakia", "en": "Slovakia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bratislava", "en": "Bratislava"}',
                'dialing_code' => '+421',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            96 => 
            array (
                'id' => 197,
                'code' => 'SI',
                'code_iso' => 'SVN',
                'title' => '{"ar": "Slovenia", "en": "Slovenia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ljubljana", "en": "Ljubljana"}',
                'dialing_code' => '+386',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            97 => 
            array (
                'id' => 198,
                'code' => 'SB',
                'code_iso' => 'SLB',
                'title' => '{"ar": "Solomon Islands", "en": "Solomon Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Honiara", "en": "Honiara"}',
                'dialing_code' => '+677',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            98 => 
            array (
                'id' => 199,
                'code' => 'SO',
                'code_iso' => 'SOM',
                'title' => '{"ar": "Somalia", "en": "Somalia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Mogadishu", "en": "Mogadishu"}',
                'dialing_code' => '+252',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            99 => 
            array (
                'id' => 200,
                'code' => 'ZA',
                'code_iso' => 'ZAF',
                'title' => '{"ar": "South Africa", "en": "South Africa"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Pretoria", "en": "Pretoria"}',
                'dialing_code' => '+27',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        \DB::table('countries')->insert(array (
            0 => 
            array (
                'id' => 201,
                'code' => 'GS',
                'code_iso' => 'SGS',
                'title' => '{"ar": "South Georgia and the South Sandwich Islands", "en": "South Georgia and the South Sandwich Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Grytviken", "en": "Grytviken"}',
                'dialing_code' => '+500',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            1 => 
            array (
                'id' => 202,
                'code' => 'SS',
                'code_iso' => 'SSD',
                'title' => '{"ar": "South Sudan", "en": "South Sudan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Juba", "en": "Juba"}',
                'dialing_code' => '+211',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            2 => 
            array (
                'id' => 203,
                'code' => 'ES',
                'code_iso' => 'ESP',
                'title' => '{"ar": "Spain", "en": "Spain"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Madrid", "en": "Madrid"}',
                'dialing_code' => '+34',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            3 => 
            array (
                'id' => 204,
                'code' => 'LK',
                'code_iso' => 'LKA',
                'title' => '{"ar": "Sri Lanka", "en": "Sri Lanka"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Colombo", "en": "Colombo"}',
                'dialing_code' => '+94',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            4 => 
            array (
                'id' => 205,
                'code' => 'SD',
                'code_iso' => 'SDN',
                'title' => '{"ar": "Sudan", "en": "Sudan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Khartoum", "en": "Khartoum"}',
                'dialing_code' => '+249',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            5 => 
            array (
                'id' => 206,
                'code' => 'SR',
                'code_iso' => 'SUR',
                'title' => '{"ar": "Suriname", "en": "Suriname"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Paramaribo", "en": "Paramaribo"}',
                'dialing_code' => '+597',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            6 => 
            array (
                'id' => 207,
                'code' => 'SJ',
                'code_iso' => 'SJM',
                'title' => '{"ar": "Svalbard and Jan Mayen", "en": "Svalbard and Jan Mayen"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Longyearbyen", "en": "Longyearbyen"}',
                'dialing_code' => '+47',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            7 => 
            array (
                'id' => 208,
                'code' => 'SZ',
                'code_iso' => 'SWZ',
                'title' => '{"ar": "Swaziland", "en": "Swaziland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Mbabane", "en": "Mbabane"}',
                'dialing_code' => '+268',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            8 => 
            array (
                'id' => 209,
                'code' => 'SE',
                'code_iso' => 'SWE',
                'title' => '{"ar": "Sweden", "en": "Sweden"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Stockholm", "en": "Stockholm"}',
                'dialing_code' => '+46',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            9 => 
            array (
                'id' => 210,
                'code' => 'CH',
                'code_iso' => 'CHE',
                'title' => '{"ar": "Switzerland", "en": "Switzerland"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Berne", "en": "Berne"}',
                'dialing_code' => '+41',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            10 => 
            array (
                'id' => 211,
                'code' => 'SY',
                'code_iso' => 'SYR',
                'title' => '{"ar": "Syria", "en": "Syria"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Damascus", "en": "Damascus"}',
                'dialing_code' => '+963',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            11 => 
            array (
                'id' => 212,
                'code' => 'TW',
                'code_iso' => 'TWN',
                'title' => '{"ar": "Taiwan", "en": "Taiwan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Taipei", "en": "Taipei"}',
                'dialing_code' => '+886',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            12 => 
            array (
                'id' => 213,
                'code' => 'TJ',
                'code_iso' => 'TJK',
                'title' => '{"ar": "Tajikistan", "en": "Tajikistan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dushanbe", "en": "Dushanbe"}',
                'dialing_code' => '+992',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            13 => 
            array (
                'id' => 214,
                'code' => 'TZ',
                'code_iso' => 'TZA',
                'title' => '{"ar": "Tanzania", "en": "Tanzania"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Dodoma", "en": "Dodoma"}',
                'dialing_code' => '+255',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            14 => 
            array (
                'id' => 215,
                'code' => 'TH',
                'code_iso' => 'THA',
                'title' => '{"ar": "Thailand", "en": "Thailand"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Bangkok", "en": "Bangkok"}',
                'dialing_code' => '+66',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            15 => 
            array (
                'id' => 216,
                'code' => 'TG',
                'code_iso' => 'TGO',
                'title' => '{"ar": "Togo", "en": "Togo"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Lome", "en": "Lome"}',
                'dialing_code' => '+228',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            16 => 
            array (
                'id' => 217,
                'code' => 'TK',
                'code_iso' => 'TKL',
                'title' => '{"ar": "Tokelau", "en": "Tokelau"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nukunonu", "en": "Nukunonu"}',
                'dialing_code' => '+690',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            17 => 
            array (
                'id' => 218,
                'code' => 'TO',
                'code_iso' => 'TON',
                'title' => '{"ar": "Tonga", "en": "Tonga"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Nuku\'alofa", "en": "Nuku\'alofa"}',
                'dialing_code' => '+676',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            18 => 
            array (
                'id' => 219,
                'code' => 'TT',
                'code_iso' => 'TTO',
                'title' => '{"ar": "Trinidad and Tobago", "en": "Trinidad and Tobago"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port of Spain", "en": "Port of Spain"}',
                'dialing_code' => '+1-868',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            19 => 
            array (
                'id' => 220,
                'code' => 'TN',
                'code_iso' => 'TUN',
                'title' => '{"ar": "Tunisia", "en": "Tunisia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tunis", "en": "Tunis"}',
                'dialing_code' => '+216',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            20 => 
            array (
                'id' => 221,
                'code' => 'TR',
                'code_iso' => 'TUR',
                'title' => '{"ar": "Turkey", "en": "Turkey"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ankara", "en": "Ankara"}',
                'dialing_code' => '+90',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            21 => 
            array (
                'id' => 222,
                'code' => 'TM',
                'code_iso' => 'TKM',
                'title' => '{"ar": "Turkmenistan", "en": "Turkmenistan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Ashgabat", "en": "Ashgabat"}',
                'dialing_code' => '+993',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            22 => 
            array (
                'id' => 223,
                'code' => 'TC',
                'code_iso' => 'TCA',
                'title' => '{"ar": "Turks and Caicos Islands", "en": "Turks and Caicos Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Cockburn Town", "en": "Cockburn Town"}',
                'dialing_code' => '+1-649',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            23 => 
            array (
                'id' => 224,
                'code' => 'TV',
                'code_iso' => 'TUV',
                'title' => '{"ar": "Tuvalu", "en": "Tuvalu"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Funafuti", "en": "Funafuti"}',
                'dialing_code' => '+688',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            24 => 
            array (
                'id' => 225,
                'code' => 'UG',
                'code_iso' => 'UGA',
                'title' => '{"ar": "Uganda", "en": "Uganda"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kampala", "en": "Kampala"}',
                'dialing_code' => '+256',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            25 => 
            array (
                'id' => 226,
                'code' => 'UA',
                'code_iso' => 'UKR',
                'title' => '{"ar": "Ukraine", "en": "Ukraine"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Kiev", "en": "Kiev"}',
                'dialing_code' => '+380',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            26 => 
            array (
                'id' => 227,
                'code' => 'AE',
                'code_iso' => 'ARE',
                'title' => '{"ar": "United Arab Emirates", "en": "United Arab Emirates"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Abu Dhabi", "en": "Abu Dhabi"}',
                'dialing_code' => '+971',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            27 => 
            array (
                'id' => 228,
                'code' => 'GB',
                'code_iso' => 'GBR',
                'title' => '{"ar": "United Kingdom", "en": "United Kingdom"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "London", "en": "London"}',
                'dialing_code' => '+44',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            28 => 
            array (
                'id' => 229,
                'code' => 'US',
                'code_iso' => 'USA',
                'title' => '{"ar": "United States", "en": "United States"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Washington", "en": "Washington"}',
                'dialing_code' => '+1',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            29 => 
            array (
                'id' => 230,
                'code' => 'UM',
                'code_iso' => 'UMI',
                'title' => '{"ar": "United States Minor Outlying Islands", "en": "United States Minor Outlying Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": ""}',
                'dialing_code' => '+1',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            30 => 
            array (
                'id' => 231,
                'code' => 'UY',
                'code_iso' => 'URY',
                'title' => '{"ar": "Uruguay", "en": "Uruguay"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Montevideo", "en": "Montevideo"}',
                'dialing_code' => '+598',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            31 => 
            array (
                'id' => 232,
                'code' => 'UZ',
                'code_iso' => 'UZB',
                'title' => '{"ar": "Uzbekistan", "en": "Uzbekistan"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Tashkent", "en": "Tashkent"}',
                'dialing_code' => '+998',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            32 => 
            array (
                'id' => 233,
                'code' => 'VU',
                'code_iso' => 'VUT',
                'title' => '{"ar": "Vanuatu", "en": "Vanuatu"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Port Vila", "en": "Port Vila"}',
                'dialing_code' => '+678',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            33 => 
            array (
                'id' => 234,
                'code' => 'VA',
                'code_iso' => 'VAT',
                'title' => '{"ar": "Vatican", "en": "Vatican"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Vatican City", "en": "Vatican City"}',
                'dialing_code' => '+379',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            34 => 
            array (
                'id' => 235,
                'code' => 'VE',
                'code_iso' => 'VEN',
                'title' => '{"ar": "Venezuela", "en": "Venezuela"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Caracas", "en": "Caracas"}',
                'dialing_code' => '+58',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            35 => 
            array (
                'id' => 236,
                'code' => 'VN',
                'code_iso' => 'VNM',
                'title' => '{"ar": "Vietnam", "en": "Vietnam"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Hanoi", "en": "Hanoi"}',
                'dialing_code' => '+84',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            36 => 
            array (
                'id' => 237,
                'code' => 'VG',
                'code_iso' => 'VGB',
                'title' => '{"ar": "British Virgin Islands", "en": "British Virgin Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Road Town", "en": "Road Town"}',
                'dialing_code' => '+1-284',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            37 => 
            array (
                'id' => 238,
                'code' => 'VI',
                'code_iso' => 'VIR',
                'title' => '{"ar": "U.S. Virgin Islands", "en": "U.S. Virgin Islands"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Charlotte Amalie", "en": "Charlotte Amalie"}',
                'dialing_code' => '+1-340',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            38 => 
            array (
                'id' => 239,
                'code' => 'WF',
                'code_iso' => 'WLF',
                'title' => '{"ar": "Wallis and Futuna", "en": "Wallis and Futuna"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Mata Utu", "en": "Mata Utu"}',
                'dialing_code' => '+681',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            39 => 
            array (
                'id' => 240,
                'code' => 'EH',
                'code_iso' => 'ESH',
                'title' => '{"ar": "Western Sahara", "en": "Western Sahara"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "El-Aaiun", "en": "El-Aaiun"}',
                'dialing_code' => '+212',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            40 => 
            array (
                'id' => 241,
                'code' => 'YE',
                'code_iso' => 'YEM',
                'title' => '{"ar": "Yemen", "en": "Yemen"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Sanaa", "en": "Sanaa"}',
                'dialing_code' => '+967',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            41 => 
            array (
                'id' => 242,
                'code' => 'ZM',
                'code_iso' => 'ZMB',
                'title' => '{"ar": "Zambia", "en": "Zambia"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Lusaka", "en": "Lusaka"}',
                'dialing_code' => '+260',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            42 => 
            array (
                'id' => 243,
                'code' => 'ZW',
                'code_iso' => 'ZWE',
                'title' => '{"ar": "Zimbabwe", "en": "Zimbabwe"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Harare", "en": "Harare"}',
                'dialing_code' => '+263',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            43 => 
            array (
                'id' => 244,
                'code' => 'BL',
                'code_iso' => 'BLM',
                'title' => '{"ar": "Saint Barthelemy", "en": "Saint Barthelemy"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Gustavia", "en": "Gustavia"}',
                'dialing_code' => '+590',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            44 => 
            array (
                'id' => 245,
                'code' => 'ME',
                'code_iso' => 'MNE',
                'title' => '{"ar": "Montenegro", "en": "Montenegro"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Podgorica", "en": "Podgorica"}',
                'dialing_code' => '+382',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            45 => 
            array (
                'id' => 246,
                'code' => 'MF',
                'code_iso' => 'MAF',
                'title' => '{"ar": "Saint Martin", "en": "Saint Martin"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Marigot", "en": "Marigot"}',
                'dialing_code' => '+590',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            46 => 
            array (
                'id' => 247,
                'code' => 'CW',
                'code_iso' => 'CUW',
                'title' => '{"ar": "Curacao", "en": "Curacao"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Willemstad", "en": "Willemstad"}',
                'dialing_code' => '+599',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            47 => 
            array (
                'id' => 248,
                'code' => 'SX',
                'code_iso' => 'SXM',
                'title' => '{"ar": "Sint Maarten", "en": "Sint Maarten"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Philipsburg", "en": "Philipsburg"}',
                'dialing_code' => '+1721',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
            48 => 
            array (
                'id' => 249,
                'code' => 'XK',
                'code_iso' => 'XKX',
                'title' => '{"ar": "Kosovo", "en": "Kosovo"}',
                'flag' => NULL,
                'capital_name' => '{"ar": "Pristina", "en": "Pristina"}',
                'dialing_code' => '+383',
                'status' => 'inactive',
                'created_at' => '2021-09-20 20:31:59',
                'updated_at' => '2021-09-20 20:31:59',
                'deleted_at' => '2021-04-03 12:15:49',
                'created_by' => NULL,
                'updated_by' => NULL,
                'deleted_by' => NULL,
            ),
        ));
        
        
    }
}