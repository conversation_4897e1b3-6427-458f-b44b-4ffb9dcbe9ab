<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->nullable()->index('doctors_user_id_foreign');
            $table->json('salutation')->nullable();
            $table->json('first_name')->nullable();
            $table->json('middle_name')->nullable();
            $table->json('last_name')->nullable();
            $table->json('full_name')->nullable();
            $table->string('full_name_ar')->nullable()->default('');
            $table->string('dialing_code', 191)->nullable();
            $table->string('other_phone', 191)->nullable();
            $table->string('ext', 191)->nullable();
            $table->date('dob')->nullable();
            $table->enum('gender', ['male', 'female', ''])->default('');
            $table->unsignedBigInteger('city_id')->nullable()->index('doctors_city_id_foreign');
            $table->string('state_code', 10)->nullable()->index('doctors_state_code_foreign');
            $table->char('country_code', 2)->nullable()->index('doctors_country_code_foreign');
            $table->json('profile_url')->nullable();
            $table->string('profile_photo', 191)->nullable();
            $table->decimal('avg_rating', 5)->nullable()->default(0);
            $table->integer('rating_count')->default(0);
            $table->integer('rating_user_count')->default(0);
            $table->integer('total_comments')->default(0);
            $table->bigInteger('visit_count')->default(0);
            $table->unsignedBigInteger('institute_id')->nullable()->index('doctors_institute_id_foreign');
            $table->unsignedBigInteger('department_id')->nullable()->index('doctors_department_id_foreign');
            $table->unsignedBigInteger('specialty_id')->nullable()->index('doctors_specialty_id_foreign');
            $table->unsignedBigInteger('subspecialty_id')->nullable()->index('doctors_subspecialty_id_foreign');
            $table->unsignedBigInteger('rank_id')->nullable()->index('doctors_rank_id_foreign');
            $table->string('schs', 191)->nullable()->unique();
            $table->string('schs_photo', 191)->nullable();
            $table->bigInteger('sehatalk_id')->nullable()->unique();
            $table->string('sehatalk_token', 191)->nullable();
            $table->boolean('is_upgraded')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->dateTime('verified_at')->nullable();
            $table->dateTime('rejected_at')->nullable();
            $table->json('reason')->nullable()->comment('Rejection Reason');
            $table->boolean('is_claimed')->default(false);
            $table->dateTime('claimed_at')->nullable();
            $table->string('suggestable_type', 191)->nullable();
            $table->unsignedBigInteger('suggestable_id')->nullable();
            $table->enum('status', ['approved', 'rejected', 'pending'])->default('pending');
            $table->json('about_me')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('doctors_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('doctors_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('doctors_deleted_by_foreign');

            $table->index(['suggestable_type', 'suggestable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
