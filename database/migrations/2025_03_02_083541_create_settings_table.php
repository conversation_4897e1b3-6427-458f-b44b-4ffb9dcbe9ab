<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->json('business_name')->nullable();
            $table->string('business_email', 191)->nullable();
            $table->string('business_dialing_code', 10)->nullable();
            $table->string('business_phone', 20)->nullable();
            $table->string('business_website', 191)->nullable();
            $table->json('business_address')->nullable();
            $table->string('support_email', 191)->nullable();
            $table->string('support_dialing_code', 10)->nullable();
            $table->string('support_phone', 20)->nullable();
            $table->char('currency_code', 3)->nullable()->index('settings_currency_code_foreign');
            $table->char('language_code', 2)->nullable()->index('settings_language_code_foreign');
            $table->string('facebook_link', 191)->nullable();
            $table->string('instagram_link', 191)->nullable();
            $table->string('twitter_link', 191)->nullable();
            $table->string('youtube_link', 191)->nullable();
            $table->string('linkedin_link', 191)->nullable();
            $table->string('mail_host', 191)->nullable();
            $table->char('mail_port', 5)->nullable();
            $table->string('mail_user_name', 191)->nullable();
            $table->string('mail_password', 191)->nullable();
            $table->enum('mail_encryption', ['none', 'tls', 'ssl'])->default('tls');
            $table->json('mail_from_name')->nullable();
            $table->string('mail_from_email', 191)->nullable();
            $table->string('no_reply_email', 191)->nullable();
            $table->string('customer_care_email', 191)->nullable();
            $table->string('sehatalk_email', 191)->nullable();
            $table->string('sms_provider', 191)->nullable();
            $table->string('sms_host', 191)->nullable();
            $table->string('sms_auth_key', 191)->nullable();
            $table->string('sms_from', 191)->nullable();
            $table->string('sendgrid_api_key', 191)->nullable();
            $table->string('hyperpay_token', 191)->nullable();
            $table->string('hyperpay_entity_id', 100)->nullable();
            $table->string('zoho_tax_id', 191)->nullable();
            $table->decimal('vat_tax')->nullable()->default(0)->comment('In Percent');
            $table->string('zoho_organization_id', 191)->nullable();
            $table->string('zoho_client_id', 191)->nullable();
            $table->string('zoho_client_secret', 191)->nullable();
            $table->string('zoho_redirect_uri', 191)->nullable();
            $table->string('zoho_code', 191)->nullable();
            $table->string('zoho_access_token', 191)->nullable()->comment('for only expires_in time - 3600 default 1 hour');
            $table->string('zoho_refresh_token', 191)->nullable()->comment('permanent and will be used to regenerate new access_token');
            $table->dateTime('zoho_access_token_expired_at')->nullable();
            $table->string('zoho_scope', 191)->nullable();
            $table->string('zoho_template_id', 191)->nullable();
            $table->string('google_analytics_key', 191)->nullable();
            $table->text('google_adsense_code_snippets')->nullable();
            $table->json('meta_keywords')->nullable();
            $table->json('meta_description')->nullable();
            $table->timestamps();
            $table->unsignedBigInteger('created_by')->nullable()->index('settings_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('settings_updated_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
