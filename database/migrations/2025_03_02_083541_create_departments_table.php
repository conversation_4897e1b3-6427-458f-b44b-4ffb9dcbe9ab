<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('st_id')->nullable();
            $table->json('title')->nullable();
            $table->string('icon', 191)->nullable();
            $table->unsignedInteger('sort')->nullable()->default(0)->comment('For Sorting Purpose');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('departments_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('departments_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('departments_deleted_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
