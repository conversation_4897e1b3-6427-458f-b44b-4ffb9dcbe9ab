<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kpis', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger('kpi_no')->nullable()->default(0);
            $table->unsignedBigInteger('department_id')->nullable();
            $table->json('title')->nullable();
            $table->json('title_remote')->nullable();
            $table->decimal('countable')->default(0)->comment('for average, (both) -> 0.50 , (institute/doctor/application) -> 1.00');
            $table->decimal('weightage_doctor')->default(0)->comment('In Percentage');
            $table->decimal('weightage_other')->default(0)->comment('In Percentage');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('kpis_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('kpis_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('kpis_deleted_by_foreign');

            $table->unique(['department_id', 'kpi_no']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpis');
    }
};
