<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('institutes', function (Blueprint $table) {
            $table->foreign(['city_id'])->references(['id'])->on('cities')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['country_code'])->references(['code'])->on('countries')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['deleted_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['institute_type_id'])->references(['id'])->on('institute_types')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['state_code'])->references(['state_code'])->on('states')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('institutes', function (Blueprint $table) {
            $table->dropForeign('institutes_city_id_foreign');
            $table->dropForeign('institutes_country_code_foreign');
            $table->dropForeign('institutes_created_by_foreign');
            $table->dropForeign('institutes_deleted_by_foreign');
            $table->dropForeign('institutes_institute_type_id_foreign');
            $table->dropForeign('institutes_state_code_foreign');
            $table->dropForeign('institutes_updated_by_foreign');
            $table->dropForeign('institutes_user_id_foreign');
        });
    }
};
