<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rating_details', function (Blueprint $table) {
            $table->foreign(['doctor_id'])->references(['id'])->on('doctors')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['rating_id'])->references(['id'])->on('ratings')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rating_details', function (Blueprint $table) {
            $table->dropForeign('rating_details_doctor_id_foreign');
            $table->dropForeign('rating_details_rating_id_foreign');
        });
    }
};
