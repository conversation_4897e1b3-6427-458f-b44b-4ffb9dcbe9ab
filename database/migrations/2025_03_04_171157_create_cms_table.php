<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->json('title')->nullable();
            $table->enum('position', ['Header', 'Footer', 'Sidebar', 'None'])->default('Header');
            $table->unsignedBigInteger('parent')->nullable()->index('cms_parent_foreign');
            $table->string('slug', 191)->nullable()->unique();
            $table->string('banner_image', 191)->nullable();
            $table->unsignedInteger('sort')->nullable()->default(0);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->json('content')->nullable();
            $table->json('meta_keywords')->nullable();
            $table->json('meta_description')->nullable();
            $table->timestamps();
            $table->unsignedBigInteger('created_by')->nullable()->index('cms_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('cms_updated_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms');
    }
};
