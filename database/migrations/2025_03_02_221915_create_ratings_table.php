<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->index('ratings_user_id_foreign');
            $table->unsignedBigInteger('doctor_id')->nullable()->index('ratings_doctor_id_foreign');
            $table->string('rateable_type', 191);
            $table->unsignedBigInteger('rateable_id');
            $table->text('comment')->nullable();
            $table->decimal('total_score_doctor')->default(0)->comment('SUM(rating_details.score_doctor)');
            $table->decimal('total_score_other')->nullable()->default(0)->comment('SUM(rating_details.score_institute/application)');
            $table->decimal('countable_doctor')->default(0)->comment('SUM(kpis.countable) WHERE kpis.apply_on IN(doctor,both)');
            $table->decimal('countable_other')->nullable()->default(0)->comment('SUM(kpis.countable) WHERE kpis.apply_on IN(institute/app,both)');
            $table->decimal('avg_score_doctor')->default(0)->comment('total_score_doctor / countable_doctor');
            $table->decimal('avg_score_other')->nullable()->default(0)->comment('total_score_other / countable_other');
            $table->decimal('avg_score')->nullable()->default(0)->comment('avg_score_doctor + (avg_score_institute | avg_score_application)');
            $table->integer('likes_count')->nullable()->default(0)->comment('Total Likes From rating_likes');
            $table->integer('flags_count')->nullable()->default(0)->comment('Total Flags From rating_flags');
            $table->enum('status', ['approved', 'visible', 'rejected', 'pending'])->default('approved')->comment('rejected = 0 (Not Calculated + Not Visible), approved = 1 (Calculated + Visible), Visible = 3 (Not Calculated Only Visible), Pending = 2 (In Approval by admin)');
            $table->unsignedBigInteger('created_by')->nullable()->index('ratings_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('ratings_updated_by_foreign');
            $table->timestamps();
            $table->unsignedTinyInteger('is_pinned_rating')->nullable()->default(0);
            $table->unsignedTinyInteger('is_hided_rating')->nullable()->default(0);

            $table->index(['rateable_type', 'rateable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
