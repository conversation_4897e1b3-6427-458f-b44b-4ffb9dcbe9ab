<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('doctors', function (Blueprint $table) {
            $table->foreign(['city_id'])->references(['id'])->on('cities')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['country_code'])->references(['code'])->on('countries')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['created_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['deleted_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['department_id'])->references(['id'])->on('departments')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['institute_id'])->references(['id'])->on('institutes')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['rank_id'])->references(['id'])->on('ranks')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['specialty_id'])->references(['id'])->on('specialties')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['state_code'])->references(['state_code'])->on('states')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['subspecialty_id'])->references(['id'])->on('subspecialties')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['updated_by'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('doctors', function (Blueprint $table) {
            $table->dropForeign('doctors_city_id_foreign');
            $table->dropForeign('doctors_country_code_foreign');
            $table->dropForeign('doctors_created_by_foreign');
            $table->dropForeign('doctors_deleted_by_foreign');
            $table->dropForeign('doctors_department_id_foreign');
            $table->dropForeign('doctors_institute_id_foreign');
            $table->dropForeign('doctors_rank_id_foreign');
            $table->dropForeign('doctors_specialty_id_foreign');
            $table->dropForeign('doctors_state_code_foreign');
            $table->dropForeign('doctors_subspecialty_id_foreign');
            $table->dropForeign('doctors_updated_by_foreign');
            $table->dropForeign('doctors_user_id_foreign');
        });
    }
};
