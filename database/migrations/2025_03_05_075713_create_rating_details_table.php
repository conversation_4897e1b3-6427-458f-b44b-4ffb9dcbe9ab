<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rating_details', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('rating_id')->index('rating_details_rating_id_foreign');
            $table->unsignedBigInteger('doctor_id')->nullable()->index('rating_details_doctor_id_foreign');
            $table->tinyInteger('kpi_no')->nullable()->default(0);
            $table->tinyInteger('rating')->default(0);
            $table->decimal('score_doctor')->default(0)->comment('rating_details.rate * ( kpis.weightage_doctor / 100)');
            $table->decimal('score_other')->nullable()->default(0)->comment('rating_details.rate * ( kpis.weightage_institute / 100)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rating_details');
    }
};
