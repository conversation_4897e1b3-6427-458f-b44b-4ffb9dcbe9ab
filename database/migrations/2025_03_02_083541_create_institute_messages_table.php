<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('institute_messages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('institute_id')->nullable()->index('institute_messages_institute_id_foreign');
            $table->string('full_name', 191)->nullable();
            $table->string('email', 191)->nullable();
            $table->string('dialing_code', 191)->nullable();
            $table->string('phone', 191)->nullable();
            $table->string('subject', 191)->nullable();
            $table->text('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('institute_messages');
    }
};
