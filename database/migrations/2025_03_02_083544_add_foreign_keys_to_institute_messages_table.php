<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('institute_messages', function (Blueprint $table) {
            $table->foreign(['institute_id'])->references(['id'])->on('institutes')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('institute_messages', function (Blueprint $table) {
            $table->dropForeign('institute_messages_institute_id_foreign');
        });
    }
};
