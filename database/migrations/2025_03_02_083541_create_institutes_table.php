<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('institutes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->nullable()->index('institutes_user_id_foreign');
            $table->json('name')->nullable();
            $table->string('institute_email', 191)->nullable();
            $table->unsignedBigInteger('institute_type_id')->nullable()->index('institutes_institute_type_id_foreign');
            $table->decimal('latitude', 11, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('logo', 191)->nullable();
            $table->string('banner', 191)->nullable();
            $table->string('dialing_code', 191)->nullable();
            $table->string('other_phone', 191)->nullable();
            $table->string('ext', 191)->nullable();
            $table->unsignedBigInteger('city_id')->nullable()->index('institutes_city_id_foreign');
            $table->string('state_code', 10)->nullable()->index('institutes_state_code_foreign');
            $table->char('country_code', 2)->nullable()->index('institutes_country_code_foreign');
            $table->tinyInteger('avg_rating')->default(0);
            $table->integer('total_comments')->default(0);
            $table->boolean('is_verified')->default(false);
            $table->dateTime('verified_at')->nullable();
            $table->dateTime('rejected_at')->nullable();
            $table->json('reason')->nullable()->comment('Rejection Reason');
            $table->boolean('is_claimed')->default(false);
            $table->dateTime('claimed_at')->nullable();
            $table->string('suggestable_type', 191)->nullable();
            $table->unsignedBigInteger('suggestable_id')->nullable();
            $table->enum('status', ['approved', 'rejected', 'pending'])->default('pending');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('institutes_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('institutes_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('institutes_deleted_by_foreign');

            $table->index(['suggestable_type', 'suggestable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('institutes');
    }
};
