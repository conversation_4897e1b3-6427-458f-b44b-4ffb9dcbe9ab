<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('parent')->nullable()->index('users_parent_foreign');
            $table->json('full_name')->nullable();
            $table->string('email', 191)->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('email_verification_code', 191)->nullable();
            $table->boolean('is_email_set')->default(true);
            $table->string('dialing_code', 191)->nullable();
            $table->string('phone', 191)->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('phone_verification_code', 191)->nullable();
            $table->enum('status', ['approved', 'rejected', 'pending', 'not_verified'])->default('pending');
            $table->boolean('is_accept_notifications')->default(true);
            $table->timestamp('code_expired_at')->nullable();
            $table->string('name', 191)->comment('Not In Use - Fetching User Role relevant Profile');
            $table->string('password', 191);
            $table->boolean('is_password_set')->default(true);
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('users_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('users_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('users_deleted_by_foreign');
            $table->timestamp('last_login_at')->nullable();
            $table->unsignedBigInteger('package_id')->nullable()->index('users_package_id_foreign');
            $table->dateTime('package_started_at')->nullable();
            $table->dateTime('package_ended_at')->nullable();
            $table->string('zoho_customer_id', 191)->nullable();
            $table->string('last_login_ip', 191)->nullable();

            $table->unique(['dialing_code', 'phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
