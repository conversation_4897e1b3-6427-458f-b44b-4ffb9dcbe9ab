<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('code', 2)->nullable()->unique();
            $table->char('code_iso', 3)->nullable()->unique();
            $table->json('title')->nullable();
            $table->string('flag', 191)->nullable();
            $table->json('capital_name')->nullable();
            $table->string('dialing_code', 191)->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('countries_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('countries_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('countries_deleted_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
