<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('country_code', 2)->nullable()->index('cities_country_code_foreign');
            $table->string('state_code', 10)->nullable()->index('cities_state_code_foreign');
            $table->json('title')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('cities_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('cities_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('cities_deleted_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
    }
};
