<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('states', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('country_code', 2)->nullable()->index('states_country_code_foreign');
            $table->string('state_code', 10)->nullable()->unique();
            $table->json('title')->nullable();
            $table->string('physical', 191)->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('created_by')->nullable()->index('states_created_by_foreign');
            $table->unsignedBigInteger('updated_by')->nullable()->index('states_updated_by_foreign');
            $table->unsignedBigInteger('deleted_by')->nullable()->index('states_deleted_by_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('states');
    }
};
