@use 'variables' as *;

.doctor-page {
    padding-top: 48px;

    .doctor-details-box {
        padding: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        text-align: center;
        border: 1px solid $gray-100;
        border-radius: 24px;
        margin-bottom: 24px;

        .doctor-image {
            width: 240px;
            height: 240px;
            border-radius: 50%;
            border: 4px solid $gray-200;
            overflow: hidden;
            margin-bottom: 22px;

            @media screen and (max-width: 768px) {
                width: 140px;
                height: 140px;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .doctor-data {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;

            .doctor-name {
                display: flex;
                align-items: center;

                h4 {
                    color: $blue-600;
                    font-size: 24px;
                    font-weight: 700;
                    margin-inline-end: 10px;
                    line-height: 1.5;
                }
            }

            .rate {
                background: $yellow-50;
                display: flex;
                width: fit-content;
                align-items: center;
                padding: 8px 12px;
                border-radius: 8px;
                margin: 12px 0;

                svg {
                    margin-inline-start: 4px;
                    width: 16px;
                    height: 16px;
                }

                span {
                    font-size: 16px;
                    font-weight: 700;
                    color: $dark-color;
                }
            }

            .specialty {
                color: $gray-600;
                font-size: 14px;
                font-weight: 400;
                display: block;
                margin-bottom: 14px;
            }

            hr {
                color: $gray-400;
                width: 100%;
                padding: 0;
                margin: 0 0 16px;
            }

            .institute {
                width: 100%;
                display: flex;
                align-items: center;

                svg {
                    margin-inline-end: 8px;
                }

                span {
                    font-size: 14px;
                    font-weight: 400;
                    color: $gray-400;
                }
            }
        }
    }

    .contact-details {
        padding: 24px;
        border-radius: 24px;
        border: 1px solid $gray-100;

        @media screen and (max-width: 768px) {
            margin-bottom: 24px;
        }

        h5 {
            color: $blue-600;
            font-size: 16px;
            font-weight: 700;
            line-height: 1.5;
            padding-bottom: 16px;
            border-bottom: 1px solid $gray-100;
            margin-bottom: 16px;
        }

        .items {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .item {
                display: flex;
                align-items: center;
                svg {
                    margin-inline-end: 8px;
                }

                span {
                    color: $gray-600;
                    font-size: 14px;
                    font-weight: 400;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .about-doctor {
        border-radius: 24px;
        border: 1px solid $gray-100;
        padding: 24px;

        h2 {
            font-size: 24px;
            color: $dark-color;
            padding-bottom: 16px;
            margin: 0 0 16px;
            border-bottom: 1px solid $gray-100;
            font-weight: 700;
        }

        h3 {
            font-size: 16px;
            color: $dark-color;
            margin: 0 0 32px;
            font-weight: 700;
        }

        .rate {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 32px;

            .avg_rating {
                color: $dark-color;
                font-size: 40px;
                text-align: center;
                display: block;
                font-weight: 700;
            }

            .stars {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 1px;

                svg {
                    height: 16px;
                    width: 16px;
                }
            }

            .reviews-count {
                padding-top: 2px;
                color: $gray-600;
                font-size: 14px;
            }
        }

        .bars {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .bar {
                display: flex;
                align-items: center;
                justify-content: space-between;

                span {
                    font-size: 14px;
                    margin: 0;
                    padding: 0 0 2px;

                    &.title {
                        width: 22%;
                        color: $gray-600;
                    }

                    &.rate {
                        width: 10%;
                        text-align: left;
                        color: $gray-300;
                    }
                }

                .bar-container {
                    border-radius: 100px;
                    background: $gray-100;
                    width: 68%;
                    height: 8px;
                    position: relative;

                    .progress {
                        top: 0;
                        right: 0;
                        bottom: 0;
                        height: 100%;
                        border-radius: 100px;
                        background: $blue-600;
                    }
                }
            }
        }
    }

    .reviews {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .review-item {
            background: $gray-50;
            padding: 16px 20px;
            border-radius: 24px;
            margin-bottom: 16px;

            .reviewer {
                display: flex;
                align-items: center;
                margin-bottom: 12px;

                img {
                    width: 50px;
                    height: 50px;
                    object-fit: cover;
                    object-position: center;
                    border-radius: 50%;
                }

                .reviewer-details {
                    margin-inline-start: 8px;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    h5 {
                        color: $dark-color;
                        padding: 0;
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0 0 4px;
                    }

                    .rate {
                        display: flex;
                        align-items: center;
                        gap: 4px;

                        .stars {
                            display: flex;
                            align-items: center;
                            gap: 1px;

                            svg {
                                height: 16px;
                                width: 16px;
                            }
                        }

                        span {
                            color: $gray-300;
                            font-size: 12px;
                        }
                    }
                }
            }

            p {
                padding: 8px 12px;
                background: #fff;
                border-radius: 12px;
                font-size: 14px;
                color: $gray-600;
                margin: 0;
            }
        }
    }

    .recommended-doctors {
        padding: 64px 0;

        @media screen and (max-width: 768px) {
            padding: 32px 0;

            .doctor-card {
                margin-bottom: 20px;
            }
        }

        h2 {
            color: $dark-color;
            font-weight: 700;
            font-size: 24px;
            line-height: 1.5;
            position: relative;
            padding: 0;
            display: inline-block;
            margin: 0 0 48px;

            @media screen and (max-width: 768px) {
                font-size: 20px;
            }

            img {
                position: absolute;
                width: 150px;
                bottom: -15px;
                left: -22px;

                @media screen and (max-width: 768px) {
                    left: 0;
                }
            }
        }
    }
}
