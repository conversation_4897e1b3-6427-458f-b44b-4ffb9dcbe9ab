@use 'variables' as *;

body {
    font-family: '<PERSON><PERSON>', sans-serif;
    direction: rtl;
    text-align: right;
}

a {
    text-decoration: none;
}

.hero {
    background-color: $blue-50;
    border-radius: 40px;
    padding: 110px 88px 0;
    display: flex;
    align-items: center;
    position: relative;

    .content {
        width: 50%;

        h1 {
            font-size: 72px;
            color: $dark-color;
            font-weight: 700;
            padding: 0;
            margin-bottom: 24px;
            position: relative;

            span {
                color: $blue-500
            }

            img {
                position: absolute;
                width: 240px;
                bottom: 0;
                right: 0;
            }
        }

        p {
            color: $gray-600;
        }
    }

    .hero-image {
        width: 58%;

        img {
            width: 100%;
            height: auto;
        }
    }

    .hero-shape {
        position: absolute;
        top: 15%;
        right: 33%;
    }

    @media screen and (max-width: 768px) {
        padding: 72px 16px 0;
        flex-direction: column;

        .content {
            text-align: center;
            width: 100%;

            h1 {
                font-size: 40px;

                img {
                    width: 160px;
                    right: 50%;
                    transform: translateX(50%);
                    bottom: -12px;
                }
            }

            p {
                color: $gray-600;
            }
        }

        .hero-image {
            width: 100%;
        }
    }
}

.achievements {
    padding: 68px 0;

    @media screen and (max-width: 768px) {
        padding: 32px 0;
    }

    .title {
        display: flex;
        justify-content: center;
        margin-bottom: 80px;

        @media screen and (max-width: 768px) {
            margin-bottom: 32px;
        }

        h2 {
            color: $dark-color;
            font-weight: 700;
            font-size: 40px;
            position: relative;
            margin: 0;
            padding: 0;

            img {
                position: absolute;
                width: 150px;
                bottom: -11px;
                right: -15px;
            }
        }

        @media screen and (max-width: 768px) {
            h2 {
                font-size: 24px;
            }
        }
    }

    .items {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media screen and (max-width: 768px) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .item {
            width: 220px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;

            @media screen and (max-width: 768px) {
                width: auto;
            }

            h4 {
                color: $blue-600;
                margin-bottom: 12px;
                font-size: 40px;
                font-weight: 700;
                text-align: left;
                direction: ltr;

                span {
                    color: $blue-200;
                    margin-inline-start: 8px;
                }
            }

            h3 {
                color: $gray-600;
                font-size: 16px;
                font-weight: 700;
                width: 100%;
                text-align: center;
            }
        }
    }
}

.categories {
    padding: 64px 0;
    position: relative;

    @media screen and (max-width: 768px) {
        padding: 32px 0;
    }

    .title {
        display: flex;
        justify-content: center;
        width: 46%;
        flex-direction: column;
        text-align: center;
        margin: 0 auto 80px;

        @media screen and (max-width: 768px) {
            margin-bottom: 32px;
        }

        @media screen and (max-width: 768px) {
            width: 100%;
        }

        h2 {
            color: $dark-color;
            font-weight: 700;
            font-size: 40px;
            position: relative;
            padding: 0;
            margin: 0 0 24px;

            @media screen and (max-width: 768px) {
                font-size: 24px;
            }

            img {
                position: absolute;
                width: 150px;
                bottom: -13px;
                right: 50%;
                transform: translateX(50%);
            }
        }

        p {
            color: $gray-600;
            font-size: 16px;
            font-weight: 400;
            padding: 0;
            margin: 0;
        }
    }

    .items {
        .item {
            border-radius: 24px;
            background: $blue-50;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            img {
                width: 112px;
                height: 112px;
                margin-bottom: 24px;
            }

            h3 {
                color: $blue-500;
                margin-bottom: 18px;
                font-size: 24px;
                font-weight: 700;
                padding: 0;
                line-height: 1.5;
            }

            p {
                color: $gray-400;
                font-size: 16px;
                font-weight: 400;
                padding: 0;
                line-height: 1.5;
                height: calc(24px * 3);
                margin: 0;
            }

            @media screen and (max-width: 768px) {
                padding: 40px 16px;
                margin-bottom: 20px;
            }
        }
    }

    .section-shape {
        position: absolute;
        top: 11%;
        right: 7%;

        @media screen and (max-width: 768px) {
            width: 60px;
            top: 0;
        }
    }
}

.top-rated-doctors {
    padding: 64px 0;

    @media screen and (max-width: 768px) {
        padding: 32px 0;
    }

    .section-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 80px;

        @media screen and (max-width: 768px) {
            padding-bottom: 24px;
            flex-direction: column;
            margin-bottom: 24px;
        }

        .title {
            display: flex;
            flex-direction: column;
            width: 50%;

            @media screen and (max-width: 768px) {
                width: 100%;
            }

            h2 {
                color: $dark-color;
                font-weight: 700;
                font-size: 40px;
                position: relative;
                padding: 0;
                margin: 0 0 24px;

                @media screen and (max-width: 768px) {
                    font-size: 24px;
                    text-align: center;
                }

                img {
                    position: absolute;
                    width: 150px;
                    bottom: -13px;
                    right: 180px;

                    @media screen and (max-width: 768px) {
                        right: 50%;
                        transform: translateX(50%);
                    }
                }
            }

            p {
                color: $gray-600;
                font-size: 16px;
                font-weight: 400;
                padding: 0;
                margin: 0;

                @media screen and (max-width: 768px) {
                    text-align: center;
                }
            }
        }

        .carousels-navigation {
            width: 50%;
            display: flex;
            justify-content: end;

            @media screen and (max-width: 768px) {
                width: 100%;
                justify-content: center;
                margin-top: 24px;
            }

            button {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: none;
                cursor: pointer;
                outline: none;
                border: none;

                svg {
                    width: 40px;
                    height: 40px;
                }

                &.prev {
                    transform: rotate(180deg);
                }
            }
        }
    }
}

.our-experience {
    padding: 64px 0;

    @media screen and (max-width: 768px) {
        padding: 32px 0;
    }

    .section-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 80px;

        @media screen and (max-width: 768px) {
            padding-bottom: 24px;
            flex-direction: column;
            margin-bottom: 24px;
        }

        .title {
            display: flex;
            flex-direction: column;
            width: 50%;

            @media screen and (max-width: 768px) {
                width: 100%;
            }

            h2 {
                color: $dark-color;
                font-weight: 700;
                font-size: 40px;
                position: relative;
                padding: 0;
                margin: 0 0 24px;

                @media screen and (max-width: 768px) {
                    font-size: 24px;
                    text-align: center;
                }

                img {
                    position: absolute;
                    width: 150px;
                    bottom: -13px;
                    right: 180px;

                    @media screen and (max-width: 768px) {
                        right: 50%;
                        transform: translateX(50%);
                    }
                }
            }

            p {
                color: $gray-600;
                font-size: 16px;
                font-weight: 400;
                padding: 0;
                margin: 0;

                @media screen and (max-width: 768px) {
                    text-align: center;
                }
            }
        }

        .carousels-navigation {
            width: 50%;
            display: flex;
            justify-content: end;

            @media screen and (max-width: 768px) {
                width: 100%;
                justify-content: center;
                margin-top: 24px;
            }

            button {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: none;
                cursor: pointer;
                outline: none;
                border: none;

                svg {
                    width: 40px;
                    height: 40px;
                }

                &.prev {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .item {
        background: $blue-50;
        padding: 40px 46px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        text-align: center;

        .quote {
            display: flex;
            justify-content: end;
            margin-bottom: 10px;
            width: 100%;
        }

        p {
            padding: 0;
            margin: 0 0 16px;
            height: calc(24px * 3);
            color: $gray-600;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }

        .rate {
            display: flex;
            align-items: center;
            gap: 1px;

            svg {
                height: 16px;
                width: 16px;
            }
        }
    }
}

.how-to-benefit-container {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom;
}

.how-to-benefit {
    padding: 64px 0;

    @media screen and (max-width: 768px) {
        padding: 32px 0;
    }

    .title {
        display: flex;
        justify-content: center;
        width: 46%;
        flex-direction: column;
        text-align: center;
        margin: 0 auto 80px;

        @media screen and (max-width: 768px) {
            width: 100%;
            margin-bottom: 24px;
        }

        h2 {
            color: $dark-color;
            font-weight: 700;
            font-size: 40px;
            position: relative;
            padding: 0;
            margin: 0 0 24px;

            @media screen and (max-width: 768px) {
                font-size: 24px;
            }

            img {
                position: absolute;
                width: 150px;
                bottom: -13px;
                right: 50%;
                transform: translateX(50%);
            }
        }
    }

    .section-image {
        img {
            width: 100%;
            height: 100%;
        }

        @media screen and (max-width: 768px) {
            margin-bottom: 32px;
        }
    }

    .items {
        display: flex;
        flex-direction: column;
        gap: 24px;

        .item {
            display: flex;
            align-items: center;
            backdrop-filter: blur(24px);
            box-shadow: 0px 4px 29px 0px #1C75BB29;
            padding: 24px;
            border-radius: 24px;


            svg {
                width: 56px;
                height: 56px;
                min-width: 56px;
                margin-inline-end: 20px;
            }

            .content {
                display: flex;
                flex-direction: column;

                h3 {
                    color: $blue-500;
                    padding: 0;
                    margin-bottom: 12px;
                    font-size: 20px;
                }

                p {
                    padding: 0;
                    margin: 0;
                    color: $gray-600;
                    font-size: 16px;
                }
            }
        }
    }
}

.app-footer {
    margin: 64px 40px 0;
    padding: 64px 52px;
    border-top-left-radius: 40px;
    border-top-right-radius: 40px;
    background: $blue-50;

    @media screen and (max-width: 768px) {
        margin: 64px 0 0;
    }

    .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 40px;
        border-bottom: 1px solid $blue-100;

        @media screen and (max-width: 768px) {
            flex-direction: column;
        }

        .logo {
            width: 92px;
            height: 56px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .links {
            display: flex;
            align-items: center;
            gap: 20px;

            @media screen and (max-width: 768px) {
                margin-top: 16px;
                flex-direction: column;
                gap: 7px;
            }

            a {
                color: $dark-color;
                padding: 8px;
                cursor: pointer;
                font-size: 16px;

                @media screen and (max-width: 768px) {
                    padding: 4px;
                }
            }
        }
    }

    .bottom-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 24px;

        @media screen and (max-width: 768px) {
            flex-direction: column;
            align-items: center;
        }

        .copyrights {
            color: $gray-600;
            font-size: 16px;

            @media screen and (max-width: 768px) {
                margin-bottom: 16px;
            }
        }

        .social-media {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .social-icon {
            svg path {
                transition: fill 0.3s ease-in-out;
            }

            &.facebook:hover svg path {
                fill: #1877F2; // Facebook blue
            }

            &.instagram:hover svg path {
                fill: #E4405F; // Instagram pink
            }

            &.twitter:hover svg path {
                fill: #1DA1F2; // Twitter blue
            }

            &.youtube:hover svg path {
                fill: #FF0000; // YouTube red
            }

            &.linkedin:hover svg path {
                fill: #0077B5; // LinkedIn blue
            }
        }

    }
}

.breadcrumbs-container {
    border-top: 1px solid $gray-100;

    .breadcrumb {
        padding: 12px 0;

        .breadcrumb-item {
            color: $gray-400;

            a {
                color: $gray-400;
            }

            &.active {
                color: $blue-500;
            }
        }
    }
}

.doctor-card {
    padding: 24px;
    border-radius: 24px;
    border: 1px solid $gray-100;
    margin: 0 1px;
    text-decoration: none;

    .doctor-data {
        display: flex;
        align-items: center;

        .doctor-image {
            width: 96px;
            height: 96px;
            min-width: 96px;
            border-radius: 50%;
            border: 2px solid $gray-200;
            overflow: hidden;

            img {
                object-fit: cover;
                width: 100%;
                height: 100%;
            }
        }

        .details {
            margin-inline-start: 20px;
            width: 100%;

            .doctor-name {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;

                h4 {
                    color: $blue-600;
                    font-size: 20px;
                    font-weight: 700;
                    padding: 0;
                    margin: 0;
                }
            }

            .category {
                display: flex;
                align-items: center;
                margin-top: 12px;

                span {
                    color: $gray-600;
                    font-size: 14px;
                }

                .rate {
                    color: $dark-color;
                    display: flex;
                    align-items: center;
                    font-weight: 700;
                    margin-inline-start: 16px;

                    svg {
                        margin-inline-start: 4px;
                    }
                }
            }
        }
    }

    .clinic-data {
        margin-top: 21px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        a, span {
            color: $gray-600;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            svg {
                margin-inline-end: 4px;
                width: 16px;
                height: 16px;
            }
        }
    }
}

.vertical-doctor-card {
    display: flex;
    align-items: center;
    flex-direction: column;
    border: 1px solid $gray-100;
    border-radius: 24px;
    padding: 24px 16px;
    margin-bottom: 20px;

    img {
        width: 120px;
        height: 120px;
        max-width: 120px;
        min-width: 120px;
        margin-bottom: 20px;
        border-radius: 50%;
        object-position: center;
        object-fit: cover;
        overflow: hidden;
        border: 4px solid $gray-200;
    }

    h3 {
        color: $blue-600;
        padding: 0;
        margin-bottom: 20px;
        font-size: 22px;
        text-align: center;
        font-weight: 700;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .rate {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .stars {
            display: flex;
            align-items: center;
            gap: 1px;

            svg {
                width: 16px;
                height: 16px;
            }
        }

        .avg-score {
            color: $dark-color;
            font-size: 16px;
            padding-inline-end: 8px;
            border-inline-end: 1px solid $gray-300;
        }

        .reviews-count {
            color: $gray-600;
            font-size: 12px;
        }
    }

    .institute {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .type {
            color: $gray-600;
            font-size: 16px;
            font-weight: 700;
        }

        .location {
            color: $gray-600;
            font-size: 16px;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }

    .btn {
        svg {
            margin-inline-start: 6px;
        }
    }
}

.ad-banner {
    img {
        width: 100%;
        object-fit: cover;
    }
}

#adModal {
    .modal-content {
        border-radius: 32px;
    }

    .modal-body {
        padding: 48px;
        position: relative;

        @media screen and (max-width: 768px) {
            padding: 32px 24px;
            padding-top: 64px;
        }

        h3 {
            color: $dark-color;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 24px;
            text-align: center;

            @media screen and (max-width: 768px) {
                font-size: 20px;
            }
        }

        .steps {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin: 48px 0;

            .step {
                display: flex;
                align-items: center;
                height: 56px;
                border-radius: 16px;
                border: 1px solid $gray-100;
                overflow: hidden;
                justify-content: space-between;
                width: 70%;
                margin: 0 auto;

                @media screen and (max-width: 768px) {
                    width: 100%;
                }

                .number {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 20px;
                    font-weight: 700;
                    color: $dark-color;
                    background-color: #E4EFFA;
                    width: 56px;
                    height: 100%;
                    min-width: 56px;
                }

                p {
                    padding: 0 16px;
                    margin: 0;
                    width: 100%;
                    text-align: right;
                }

                img {
                    margin-left: 16px;
                }
            }
        }

        .mobile-form {
            border-radius: 12px;
            background-color: $blue-100;
            padding: 16px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            
            h4 {
                font-weight: 700;
                color: $dark-color;
                font-size: 20px;
                margin-bottom: 24px;
                text-align: center;
            }

            .mobile-form-group {
                .form-group {
                    display: flex;
                    align-items: center;
                    background-color: #fff;
                    padding: 0;
                    height: 50px;
                    border-radius: 100px;
                    overflow: hidden;
                    border: 1px solid $gray-200;
    
                    .country-code {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 0 20px;
                        border-inline-start: 1px solid $gray-200;
                        height: 100%;

                        @media screen and (max-width: 768px) {
                            padding: 0 14px;
                        }

                        span {
                            direction: ltr;
                        }
                    }
    
                    input {
                        border: none;
                        background-color: transparent;
                        padding: 0 20px;
                        width: 100%;
                        height: 100%;
                        text-align: left;
                        direction: ltr;
    
                        &:focus {
                            outline: none;
                        }
                    }
                }

                .btn {
                    font-weight: 700;
                    color: #fff;
                    background-color: $blue-600;
                }
            }

            .crown {
                position: absolute;
                top: -58px;
                right: -50px;
                width: 110px;

                @media screen and (max-width: 768px) {
                    top: -40px;
                    right: -40px;
                    width: 80px;
                }
            }
        }

        .close {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 40px;   
            cursor: pointer;
        }
    }
}