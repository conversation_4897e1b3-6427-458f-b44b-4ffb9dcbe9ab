@use 'variables' as *;

.institutes-page {
    .page-header {
        border-radius: 40px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 427px;
        background-size: cover;
        overflow: hidden;
        position: relative;
        margin-bottom: 64px;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: $dark-color;
            opacity: 0.5;
        }

        .institutes-details {
            padding: 48px 32px;
            width: 50%;
            border-radius: 12px;
            background: rgba(246, 249, 255, 0.70);
            box-shadow: 0 0 29px 0 rgba(42, 167, 255, 0.16);
            backdrop-filter: blur(12px);
            display: flex;
            flex-direction: column;
            gap: 16px;

            @media screen and (max-width: 768px) {
                width: 90%;
            }

            h1 {
                color: $dark-color;
                font-size: 24px;
                font-weight: 700;
                padding: 0 0 4px;
            }

            .item {
                display: flex;
                align-items: center;
                gap: 8px;

                svg {
                    width: 24px;
                    height: 24px;
                }

                span {
                    color: $dark-color;
                    font-size: 16px;
                    font-weight: 400;
                }
            }
        }
    }
}
