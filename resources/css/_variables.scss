@use "sass:color";

$blue-50: #F2F8FD;
$blue-100: #E4EFFA;
$blue-200: #C2DFF5;
$blue-500: #4FA6E1;
$blue-600: #1B75BB;
$dark-color: #102841;
$gray-50: #F6F7F9;
$gray-100: #EDEEF1;
$gray-200: #D6DBE1;
$gray-300: #B3BDC6;
$gray-400: #8999A7;
$gray-600: #5D6D7E;
$yellow-50: #FFFFEA;

.btn {
    border-radius: 100px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.btn-blue-600 {
    background: $blue-600;
    color: white;
    font-size: 16px;
    font-weight: 700;

    &:hover {
        background: color.adjust($blue-600, $lightness: -10%);
        color: #fff;
    }
}
