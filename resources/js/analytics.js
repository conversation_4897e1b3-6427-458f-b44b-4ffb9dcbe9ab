// GA4 Measurement ID
const MEASUREMENT_ID = 'G-WND7ZB71B1';

// Debug logging function
function debugLog(message, data = null) {
    if (process.env.NODE_ENV !== 'production') {
        if (data) {
            console.log(`[GA4 Debug] ${message}`, data);
        } else {
            console.log(`[GA4 Debug] ${message}`);
        }
    }
}

// Initialize GA4
function initializeGA4() {
    window.dataLayer = window.dataLayer || [];
    function gtag() {
        dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', MEASUREMENT_ID, {
        debug_mode: process.env.NODE_ENV !== 'production'
    });
    debugLog('GA4 initialized');
}

// Track page view
function trackPageView() {
    gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: window.location.pathname
    });
    debugLog('Page view tracked', { title: document.title });
}

// Track scroll start
function trackScrollStart() {
    let hasScrolled = false;
    window.addEventListener('scroll', function() {
        if (!hasScrolled) {
            gtag('event', 'scroll_started', {
                page_title: document.title,
                page_location: window.location.href
            });
            debugLog('Scroll started tracked');
            hasScrolled = true;
        }
    }, { once: true });
}

// Track doctor card click
function trackDoctorCardClick() {
    document.addEventListener('click', function(e) {
        const doctorCard = e.target.closest('.doctor-card, .vertical-doctor-card');
        if (doctorCard) {
            e.preventDefault(); // Prevent immediate navigation
            
            const doctorId = doctorCard.dataset.doctorId;
            const doctorName = doctorCard.dataset.doctorName;
            
            // Send the event
            gtag('event', 'doctor_card_clicked', {
                doctor_id: doctorId,
                doctor_name: doctorName,
                page_title: document.title,
                page_location: window.location.href
            });
            
            // Wait a brief moment to ensure the event is sent
            setTimeout(() => {
                window.location.href = doctorCard.href;
            }, 100);
            
            debugLog('Doctor card clicked', { name: doctorName, id: doctorId });
        }
    });
}

// Track doctors slider swipes
function trackDoctorsSliderSwipe() {
    const slider = document.querySelector('.top-rated-doctors .owl-carousel');
    if (slider) {
        // Track manual navigation (arrows)
        const nextButton = document.querySelector('.top-rated-doctors .carousels-navigation .next');
        const prevButton = document.querySelector('.top-rated-doctors .carousels-navigation .prev');
        
        if (nextButton) {
            nextButton.addEventListener('click', () => {
                gtag('event', 'doctors_slider_swiped', {
                    direction: 'next',
                    page_title: document.title,
                    page_location: window.location.href
                });
                debugLog('Doctors slider swiped', { direction: 'next' });
            });
        }
        
        if (prevButton) {
            prevButton.addEventListener('click', () => {
                gtag('event', 'doctors_slider_swiped', {
                    direction: 'prev',
                    page_title: document.title,
                    page_location: window.location.href
                });
                debugLog('Doctors slider swiped', { direction: 'prev' });
            });
        }
        
        // Track touch and mouse swipes
        let startX = 0;
        let endX = 0;
        let isDragging = false;
        
        // Mouse events
        slider.addEventListener('mousedown', (e) => {
            startX = e.clientX;
            isDragging = true;
        });
        
        slider.addEventListener('mousemove', (e) => {
            if (isDragging) {
                endX = e.clientX;
            }
        });
        
        slider.addEventListener('mouseup', (e) => {
            if (isDragging) {
                const diff = startX - endX;
                if (Math.abs(diff) > 50) {
                    const direction = diff > 0 ? 'next' : 'prev';
                    gtag('event', 'doctors_slider_swiped', {
                        direction: direction,
                        page_title: document.title,
                        page_location: window.location.href
                    });
                    debugLog('Doctors slider swiped', { direction: direction });
                }
                isDragging = false;
            }
        });
        
        // Touch events
        slider.addEventListener('touchstart', (e) => {
            startX = e.changedTouches[0].screenX;
        }, { passive: true });
        
        slider.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].screenX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) {
                const direction = diff > 0 ? 'next' : 'prev';
                gtag('event', 'doctors_slider_swiped', {
                    direction: direction,
                    page_title: document.title,
                    page_location: window.location.href
                });
                debugLog('Doctors slider swiped', { direction: direction });
            }
        }, { passive: true });
        
        // Handle mouse leave while dragging
        slider.addEventListener('mouseleave', () => {
            isDragging = false;
        });
    }
}

// Track second opinion CTA and modal
function trackSecondOpinion() {
    // Track CTA clicks
    const adBanners = document.querySelectorAll('.ad-banner');
    adBanners.forEach(banner => {
        banner.addEventListener('click', () => {
            gtag('event', 'second_opinion_cta_clicked', {
                page_title: document.title,
                page_location: window.location.href
            });
            debugLog('Second opinion CTA clicked');
        });
    });

    // Track modal opens and closes
    const adModal = document.getElementById('adModal');
    if (adModal) {
        // Track modal open
        adModal.addEventListener('show.bs.modal', () => {
            gtag('event', 'second_opinion_modal_opened', {
                page_title: document.title,
                page_location: window.location.href
            });
            debugLog('Second opinion modal opened');
        });

        // Track modal close
        adModal.addEventListener('hide.bs.modal', () => {
            const form = adModal.querySelector('#secondOpinionForm');
            const phoneInput = form.querySelector('#phone_number');
            
            // If the form exists and phone number is empty, track modal close
            if (form && (!phoneInput || !phoneInput.value)) {
                gtag('event', 'second_opinion_modal_closed', {
                    page_title: document.title,
                    page_location: window.location.href
                });
                debugLog('Second opinion modal closed without submission');
            }
        });
    }

    // Track form submission
    const form = document.getElementById('secondOpinionForm');
    if (form) {
        form.addEventListener('submit', (e) => {
            const phoneInput = form.querySelector('#phone_number');
            if (phoneInput && phoneInput.value) {
                gtag('event', 'second_opinion_form_submit', {
                    phone_number: phoneInput.value,
                    page_title: document.title,
                    page_location: window.location.href
                });
                debugLog('Second opinion form submitted', { phone: phoneInput.value });
            }
        });
    }
}

// Initialize all tracking
document.addEventListener('DOMContentLoaded', function() {
    debugLog('Initializing GA4 tracking...');
    initializeGA4();
    trackPageView();
    trackScrollStart();
    trackDoctorCardClick();
    trackDoctorsSliderSwipe();
    trackSecondOpinion();
    debugLog('GA4 tracking initialized');
}); 