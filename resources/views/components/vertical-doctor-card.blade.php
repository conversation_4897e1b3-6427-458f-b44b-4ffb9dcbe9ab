<a href="{{ route('doctors.show', ['doctor' => $doctor->id, 'slug' => $doctor->slug]) }}" 
   class="vertical-doctor-card"
   data-doctor-id="{{ $doctor->id }}"
   data-doctor-name="{{ $doctor->localizedName }}">
    <img src="{{ $doctor->profile_photo_url ?? '' }}" alt="{{ $doctor->localizedName }}">

    <h3> {{ $doctor->localizedName }} </h3>

    <div class="rate">
        <div class="stars">
            @for ($i = 0; $i < floor($doctor->avg_rating); $i++)
                <svg width="14" height="13" viewBox="0 0 14 13" fill="#F6C000" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.52447 0.463524C6.67415 0.00286841 7.32585 0.00286996 7.47553 0.463525L8.68386 4.18237C8.75079 4.38838 8.94277 4.52786 9.15938 4.52786H13.0696C13.554 4.52786 13.7554 5.14767 13.3635 5.43237L10.2001 7.73075C10.0248 7.85807 9.95149 8.08375 10.0184 8.28976L11.2268 12.0086C11.3764 12.4693 10.8492 12.8523 10.4573 12.5676L7.29389 10.2693C7.11865 10.1419 6.88135 10.1419 6.70611 10.2693L3.54267 12.5676C3.15081 12.8523 2.62357 12.4693 2.77325 12.0086L3.98157 8.28976C4.04851 8.08375 3.97518 7.85807 3.79994 7.73075L0.636495 5.43237C0.244639 5.14767 0.446028 4.52786 0.93039 4.52786H4.84062C5.05723 4.52786 5.24921 4.38838 5.31614 4.18237L6.52447 0.463524Z"/>
                </svg>
            @endfor
        </div>

        <span class="avg-score">{{ intval($doctor->avg_rating) ?? '-' }}</span>

        <span class="reviews-count"> ({{ $doctor->rating_count }} تقييم) </span>
    </div>

    <div class="institute">
        <span class="type"> {{ $doctor->specialty->localizedTitle ?? '-' }} </span>

        <span class="location">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.4" d="M13.7466 5.63335C13.0466 2.55335 10.3599 1.16669 7.99994 1.16669C7.99994 1.16669 7.99994 1.16669 7.99327 1.16669C5.63994 1.16669 2.94661 2.54669 2.24661 5.62669C1.46661 9.06669 3.57328 11.98 5.47994 13.8134C6.18661 14.4934 7.09328 14.8334 7.99994 14.8334C8.90661 14.8334 9.81328 14.4934 10.5133 13.8134C12.4199 11.98 14.5266 9.07335 13.7466 5.63335Z" fill="#8999A7"/>
                <path d="M8.00002 8.97332C9.15982 8.97332 10.1 8.03311 10.1 6.87332C10.1 5.71352 9.15982 4.77332 8.00002 4.77332C6.84023 4.77332 5.90002 5.71352 5.90002 6.87332C5.90002 8.03311 6.84023 8.97332 8.00002 8.97332Z" fill="#8999A7"/>
            </svg>

            <span> {{ $doctor->city->localizedTitle ?? '-' }} </span>
        </span>
    </div>

    <button class="btn btn-blue-600">
        عرض المزيد
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.5332 6.5972H4.842L8.05374 3.38546C8.59525 2.84395 8.59525 1.94765 8.05374 1.40614C7.51222 0.864622 6.61592 0.864622 6.07441 1.40614L0.472542 7.008C-0.0689722 7.54952 -0.0689722 8.44581 0.472542 8.98733L6.07441 14.5892C6.3545 14.8693 6.70929 15 7.06407 15C7.41886 15 7.77364 14.8693 8.05374 14.5892C8.59525 14.0477 8.59525 13.1514 8.05374 12.6099L4.842 9.39813H14.5332C15.2988 9.39813 15.9337 8.76325 15.9337 7.99767C15.9337 7.23208 15.2988 6.5972 14.5332 6.5972Z" fill="white"/>
        </svg>
    </button>
</a>
