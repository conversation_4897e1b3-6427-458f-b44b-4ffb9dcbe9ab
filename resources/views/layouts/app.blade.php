<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="@yield('meta_description', 'قيم دكتوري هي منصة إلكترونية للأطباء والمرضى في المملكة العربية السعودية. تمكن المرضى من البحث عن أفضل الأطباء والمعاير الطبية المتاحة في المملكة، والحجز والاستفسار عن المعاير الطبية المتاحة، والتواصل مع الأطباء والمعاير الطبية المتاحة، والحجز والاستفسار عن المعاير الطبية المتاحة، والتواصل مع الأطباء والمعاير الطبية المتاحة')">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'My Laravel Project')</title>

    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-WND7ZB71B1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-WND7ZB71B1');
    </script>
    <!-- End Google Analytics 4 -->

    <!-- Preload critical assets -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" as="style">

    <!-- Preload hero image -->
    <!-- <link rel="preload" as="image" href="{{ asset('assets/images/hero-img1.webp') }}" type="image/webp"> -->
    <!-- <link rel="preload" as="image" href="{{ asset('assets/images/how-to-benefit.webp') }}" type="image/webp"> -->

    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">

    <!-- Load critical CSS -->
    <style>
        .navbar {
            padding: 24px 0 !important;
        }

        .navbar .navbar-brand {
            padding: 0;
        }

        .navbar .navbar-brand img {
            height: 56px;
            width: auto;
        }

        .navbar .navbar-nav .nav-item {
            margin-inline-start: 64px;
        }

        .navbar .navbar-nav .nav-item .nav-link {
            color: #1C1C1C;
            font-size: 16px;
            padding: 0 8px;
        }

        .navbar .navbar-nav .nav-item .nav-link.active {
            color: #1C75BB;
            font-weight: 700;
        }

        @media screen and (max-width: 768px) {
            .navbar .navbar-nav .nav-item {
                margin-inline-start: 0;
                margin-block-start: 16px;
            }
        }

        .navbar .navbar-toggler {
            padding: 0;
            margin: 0;
        }

        .navbar .navbar-toggler svg {
            width: 36px;
            height: 36px;
        } 
    </style>

    <!-- Load non-critical CSS asynchronously -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" media="all">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700&display=swap" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css" media="print" onload="this.media='all'">

    @vite('resources/css/main.scss')

    @yield('styles')

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-NVBV3R4S');</script>
    <!-- End Google Tag Manager -->
    
    @yield('schema')
</head>
<body>

@include('partials.navbar')

<main>
    @yield('content')
</main>

@include('partials.footer')

<!-- Defer non-critical JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>

@yield('scripts')

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NVBV3R4S"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    
<!-- Ad Modal -->
<div class="modal fade" id="adModal" tabindex="-1" aria-labelledby="adModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <h3>
                    اطمئن أكثر وابدأ الآن بخطوات سهلة   
                </h3>

                <div class="steps">
                    <div class="step">
                        <div class="number">01</div>
                        <p> اختر التخصص المناسب لحالتك. </p>
                        <img src="{{ asset('assets/images/checked.svg') }}" alt="checked">
                    </div>

                    <div class="step">
                        <div class="number">02</div>
                        <p>اختر الطبيب الذي تفضّله. </p>
                        <img src="{{ asset('assets/images/checked.svg') }}" alt="checked">
                    </div>

                    <div class="step">
                        <div class="number">03</div>
                        <p> أكمل الدفع بأمان. </p>
                        <img src="{{ asset('assets/images/checked.svg') }}" alt="checked">
                    </div>

                    <div class="step">
                        <div class="number">04</div>
                        <p> تواصل نصيباً مع الطبيب خلال 48 ساعة. </p>
                        <img src="{{ asset('assets/images/chat.svg') }}" alt="checked">
                    </div>
                </div>

                <div class="mobile-form">
                    <h4> كن أول من يجرب هذه الميزة الجديدة! سجل رقمك الآن </h4>

                    <form id="secondOpinionForm" action="{{ route('second-opinion.store') }}" method="POST">
                        @csrf
                        <div class="d-flex align-items-center gap-3 mobile-form-group flex-column flex-md-row">
                            <div class="form-group">
                                <!-- phone number -->
                                <input type="text" 
                                    name="phone_number" 
                                    id="phone_number" 
                                    placeholder="512345678" 
                                    required 
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                    title="الرجاء إدخال 9 أرقام فقط">

                                    <!-- country code with flag -->
                                    <div class="country-code">
                                        <span>+966</span>
                                        <img src="{{ asset('assets/images/saudiflag.svg') }}" alt="sa">
                                    </div>
                            </div>

                            <button type="submit" class="btn btn-primary"> انضم لقائمة الانتظار </button>
                        </div>
                    </form>

                    <img src="{{ asset('assets/images/crown.png') }}" alt="crown" class="crown">
                </div>

                <img src="{{ asset('assets/images/close.svg') }}" alt="close" class="close" data-bs-dismiss="modal">
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('secondOpinionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const phoneNumber = formData.get('phone_number');
    
    // Validate phone number length
    if (phoneNumber.length !== 9) {
        alert('الرجاء إدخال 9 أرقام فقط');
        return;
    }
    
    // Add country code
    formData.set('phone_number', '+966' + phoneNumber);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إرسال طلبك بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('adModal')).hide();
            this.reset();
        } else {
            alert(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ، يرجى المحاولة مرة أخرى');
    });
});
</script>

<script>
    // Move carousel initialization to a separate file and load it with defer
    document.addEventListener('DOMContentLoaded', function() {
        var topRatedOwl = $(".top-rated-doctors .owl-carousel").owlCarousel({
            rtl: true,
            loop: true,
            margin: 16,
            nav: false,
            dots: false,
            responsive:{
                0:{ items:1 },
                768:{ items:3 }
            }
        });

        // Custom Buttons
        $(".top-rated-doctors .carousels-navigation .next").click(function() {
            topRatedOwl.trigger("next.owl.carousel");
        });

        $(".top-rated-doctors .carousels-navigation .prev").click(function() {
            topRatedOwl.trigger("prev.owl.carousel");
        });

        var ourExperienceOwl = $(".our-experience .owl-carousel").owlCarousel({
            rtl: true,
            loop: true,
            margin: 16,
            nav: false,
            dots: false,
            responsive:{
                0:{ items:1 },
                768:{ items:4 }
            }
        });

        // Custom Buttons
        $(".our-experience .carousels-navigation .next").click(function() {
            ourExperienceOwl.trigger("next.owl.carousel");
        });

        $(".our-experience .carousels-navigation .prev").click(function() {
            ourExperienceOwl.trigger("prev.owl.carousel");
        });
    });
</script>

@vite('resources/js/analytics.js')
</body>
</html>
