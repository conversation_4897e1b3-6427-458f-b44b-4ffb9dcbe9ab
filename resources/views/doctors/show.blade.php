@extends('layouts.app')

@section('styles')
    @vite('resources/css/doctor-show.scss')
@endsection

@section('schema')
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "{{ $doctor->full_name_ar }}",
            "jobTitle": "{{ $doctor->specialty->localizedTitle ?? '-' }}",
            "image": "{{ $doctor->profile_photo_url }}",
            "url": "{{ route('doctors.show', ['doctor' => $doctor->id, 'slug' => $doctor->slug]) }}",
            "worksFor": {
                "@type": "Organization",
                "name": "{{ $doctor->institute->localizedName }}",
                "address": {
                    "@type": "PostalAddress",
                    "addressCountry": "{{ optional($doctor->institute->country)->localizedTitle }}",
                    "addressRegion": "{{ optional($doctor->institute->state)->localizedTitle }}",
                    "addressLocality": "{{ optional($doctor->institute->city)->localizedTitle }}"
                },
                "telephone": "{{ $doctor->institute->phone }}",
                "email": "{{ $doctor->institute->institute_email }}"
            },
            "address": {
                "@type": "PostalAddress",
                "addressCountry": "{{ optional($doctor->country)->localizedTitle }}",
                "addressRegion": "{{ optional($doctor->state)->localizedTitle }}",
                "addressLocality": "{{ optional($doctor->city)->localizedTitle }}"
            },
            "telephone": "{{ $doctor->dialing_code }} {{ $doctor->other_phone }}",
            "email": "{{ $doctor->email }}",
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "{{ $doctor->avg_rating }}",
                "ratingCount": "{{ $doctor->ratings_count }}",
                "bestRating": "5",
                "worstRating": "1"
            },
            "sameAs": [
                @if($doctor->facebook) "{{ $doctor->facebook }}", @endif
                @if($doctor->twitter) "{{ $doctor->twitter }}", @endif
                @if($doctor->linkedin) "{{ $doctor->linkedin }}", @endif
                @if($doctor->instagram) "{{ $doctor->instagram }}" @endif
            ]
        }
    </script>
@endsection

@section('title')
    {{ $doctor->localizedName }}
@endsection

@section('content')
    @include('partials.breadcrumbs', ['breadcrumbs' => [
        ['name' => 'الرئيسية', 'url' => route('home')],
        ['name' => 'تفاصيل الطبيب']
    ]])

    <div class="container">
        <div class="doctor-page">
            <div class="row">
                <div class="col-md-4">
                    <div class="doctor-details-box">
                        <div class="doctor-image">
                            <img src="{{ $doctor->profile_photo_url ?? '' }}" alt="{{ $doctor->localizedName }}">
                        </div>

                        <div class="doctor-data">
                            <div class="doctor-name">
                                <h4>
                                    {{ $doctor->localizedName }}
                                </h4>

                                <div class="verify-badge">
                                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.4" d="M11.25 3.01672C11.94 2.42672 13.07 2.42672 13.77 3.01672L15.35 4.37672C15.65 4.63672 16.21 4.84672 16.61 4.84672H18.31C19.37 4.84672 20.24 5.71672 20.24 6.77672V8.47672C20.24 8.86672 20.45 9.43672 20.71 9.73672L22.07 11.3167C22.66 12.0067 22.66 13.1367 22.07 13.8367L20.71 15.4167C20.45 15.7167 20.24 16.2767 20.24 16.6767V18.3767C20.24 19.4367 19.37 20.3067 18.31 20.3067H16.61C16.22 20.3067 15.65 20.5167 15.35 20.7767L13.77 22.1367C13.08 22.7267 11.95 22.7267 11.25 22.1367L9.67 20.7767C9.37 20.5167 8.81 20.3067 8.41 20.3067H6.68C5.62 20.3067 4.75 19.4367 4.75 18.3767V16.6667C4.75 16.2767 4.54 15.7167 4.29 15.4167L2.94 13.8267C2.36 13.1367 2.36 12.0167 2.94 11.3267L4.29 9.73672C4.54 9.43672 4.75 8.87672 4.75 8.48672V6.76672C4.75 5.70672 5.62 4.83672 6.68 4.83672H8.41C8.8 4.83672 9.37 4.62672 9.67 4.36672L11.25 3.01672Z" fill="#4FB9E6"/>
                                        <path d="M11.2901 15.7367C11.0901 15.7367 10.9001 15.6567 10.7601 15.5167L8.34006 13.0967C8.05006 12.8067 8.05006 12.3267 8.34006 12.0367C8.63006 11.7467 9.11006 11.7467 9.40006 12.0367L11.2901 13.9267L15.5901 9.62674C15.8801 9.33674 16.3601 9.33674 16.6501 9.62674C16.9401 9.91674 16.9401 10.3967 16.6501 10.6867L11.8201 15.5167C11.6801 15.6567 11.4901 15.7367 11.2901 15.7367Z" fill="#4FB9E6"/>
                                    </svg>
                                </div>
                            </div>

                            <div class="rate">
                                <span>
                                    {{ intval($doctor->avg_rating) ?? '-' }}/5
                                </span>

                                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.52447 1.53024C7.67415 1.06958 8.32585 1.06958 8.47553 1.53024L9.68386 5.24908C9.75079 5.4551 9.94277 5.59458 10.1594 5.59458H14.0696C14.554 5.59458 14.7554 6.21438 14.3635 6.49908L11.2001 8.79746C11.0248 8.92478 10.9515 9.15046 11.0184 9.35647L12.2268 13.0753C12.3764 13.536 11.8492 13.919 11.4573 13.6343L8.29389 11.336C8.11865 11.2086 7.88135 11.2086 7.70611 11.336L4.54267 13.6343C4.15081 13.919 3.62357 13.536 3.77325 13.0753L4.98157 9.35647C5.04851 9.15046 4.97518 8.92478 4.79994 8.79746L1.6365 6.49908C1.24464 6.21438 1.44603 5.59458 1.93039 5.59458H5.84062C6.05723 5.59458 6.24921 5.4551 6.31614 5.24908L7.52447 1.53024Z" fill="#F6C000"/>
                                </svg>
                            </div>

                            <span class="specialty">
                                {{ $doctor->specialty->localizedTitle ?? '-' }}
                            </span>

                            <hr>

                            <a href="{{ route('institutes.show', ['institute' => $doctor->institute->id, 'slug' => $doctor->institute->slug]) }}" class="institute">
                                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4" d="M1.40002 14.7334V4.08671C1.40002 2.74671 2.06673 2.07336 3.39339 2.07336H7.54671C8.87338 2.07336 9.53337 2.74671 9.53337 4.08671V14.7334" fill="#8999A7"/>
                                    <path d="M7.16671 6.06671H3.8667C3.59337 6.06671 3.3667 5.84004 3.3667 5.56671C3.3667 5.29338 3.59337 5.06671 3.8667 5.06671H7.16671C7.44004 5.06671 7.66671 5.29338 7.66671 5.56671C7.66671 5.84004 7.44004 6.06671 7.16671 6.06671Z" fill="#8999A7"/>
                                    <path d="M7.16671 8.56671H3.8667C3.59337 8.56671 3.3667 8.34004 3.3667 8.06671C3.3667 7.79338 3.59337 7.56671 3.8667 7.56671H7.16671C7.44004 7.56671 7.66671 7.79338 7.66671 8.06671C7.66671 8.34004 7.44004 8.56671 7.16671 8.56671Z" fill="#8999A7"/>
                                    <path d="M5.5 15.2334C5.22667 15.2334 5 15.0067 5 14.7334V12.2334C5 11.9601 5.22667 11.7334 5.5 11.7334C5.77333 11.7334 6 11.9601 6 12.2334V14.7334C6 15.0067 5.77333 15.2334 5.5 15.2334Z" fill="#8999A7"/>
                                    <path d="M15.3333 14.2334H13.8199V12.2334C14.4533 12.0267 14.9133 11.4334 14.9133 10.7334V9.40002C14.9133 8.52669 14.2 7.81335 13.3266 7.81335C12.4533 7.81335 11.7399 8.52669 11.7399 9.40002V10.7334C11.7399 11.4267 12.1933 12.0134 12.8133 12.2267V14.2334H0.666626C0.393293 14.2334 0.166626 14.46 0.166626 14.7334C0.166626 15.0067 0.393293 15.2334 0.666626 15.2334H13.2866C13.3 15.2334 13.3066 15.24 13.3199 15.24C13.3333 15.24 13.34 15.2334 13.3533 15.2334H15.3333C15.6066 15.2334 15.8333 15.0067 15.8333 14.7334C15.8333 14.46 15.6066 14.2334 15.3333 14.2334Z" fill="#8999A7"/>
                                </svg>

                                <span> {{ $doctor->institute->localizedName }} </span>
                            </a>
                        </div>
                    </div>

                    <div class="contact-details">
                        <h5> معلومات التواصل </h5>

                        <div class="items">
                            <div class="item">
                                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4" d="M10.8266 1.40002H5.17329C3.33329 1.40002 2.66663 2.06669 2.66663 3.94002V12.1934C2.66663 14.0667 3.33329 14.7334 5.17329 14.7334H10.82C12.6666 14.7334 13.3333 14.0667 13.3333 12.1934V3.94002C13.3333 2.06669 12.6666 1.40002 10.8266 1.40002Z" fill="#8999A7"/>
                                    <path d="M9.33329 4.2334H6.66663C6.39329 4.2334 6.16663 4.00673 6.16663 3.7334C6.16663 3.46007 6.39329 3.2334 6.66663 3.2334H9.33329C9.60663 3.2334 9.83329 3.46007 9.83329 3.7334C9.83329 4.00673 9.60663 4.2334 9.33329 4.2334Z" fill="#8999A7"/>
                                    <path d="M8.00004 12.9334C8.64437 12.9334 9.16671 12.411 9.16671 11.7667C9.16671 11.1224 8.64437 10.6 8.00004 10.6C7.35571 10.6 6.83337 11.1224 6.83337 11.7667C6.83337 12.411 7.35571 12.9334 8.00004 12.9334Z" fill="#8999A7"/>
                                </svg>

                                <span> {{ $doctor->dialing_code }} {{ $doctor->other_phone }} </span>
                            </div>

                            <div class="item">
                                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4" d="M13.7466 5.70006C13.0466 2.62006 10.3599 1.2334 7.99994 1.2334C7.99994 1.2334 7.99994 1.2334 7.99327 1.2334C5.63994 1.2334 2.94661 2.6134 2.24661 5.6934C1.46661 9.1334 3.57328 12.0467 5.47994 13.8801C6.18661 14.5601 7.09328 14.9001 7.99994 14.9001C8.90661 14.9001 9.81328 14.5601 10.5133 13.8801C12.4199 12.0467 14.5266 9.14006 13.7466 5.70006Z" fill="#8999A7"/>
                                    <path d="M8.00002 9.04003C9.15982 9.04003 10.1 8.09983 10.1 6.94003C10.1 5.78023 9.15982 4.84003 8.00002 4.84003C6.84023 4.84003 5.90002 5.78023 5.90002 6.94003C5.90002 8.09983 6.84023 9.04003 8.00002 9.04003Z" fill="#8999A7"/>
                                </svg>

                                <span>
                                    {{ $doctor->city->localizedTitle }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="about-doctor">
                        <h2> نبذة عني </h2>

                        <h3> التقييم العام </h3>

                        <div class="rate">
                            <span class="avg_rating">
                                {{ intval($doctor->avg_rating) ?? '-' }}
                            </span>

                            <div class="stars">
                                @for ($i = 0; $i < floor($doctor->avg_rating); $i++)
                                    <svg width="14" height="13" viewBox="0 0 14 13" fill="#F6C000" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6.52447 0.463524C6.67415 0.00286841 7.32585 0.00286996 7.47553 0.463525L8.68386 4.18237C8.75079 4.38838 8.94277 4.52786 9.15938 4.52786H13.0696C13.554 4.52786 13.7554 5.14767 13.3635 5.43237L10.2001 7.73075C10.0248 7.85807 9.95149 8.08375 10.0184 8.28976L11.2268 12.0086C11.3764 12.4693 10.8492 12.8523 10.4573 12.5676L7.29389 10.2693C7.11865 10.1419 6.88135 10.1419 6.70611 10.2693L3.54267 12.5676C3.15081 12.8523 2.62357 12.4693 2.77325 12.0086L3.98157 8.28976C4.04851 8.08375 3.97518 7.85807 3.79994 7.73075L0.636495 5.43237C0.244639 5.14767 0.446028 4.52786 0.93039 4.52786H4.84062C5.05723 4.52786 5.24921 4.38838 5.31614 4.18237L6.52447 0.463524Z"/>
                                    </svg>
                                @endfor
                            </div>

                            <span class="reviews-count">
                                120 تقييم
                            </span>
                        </div>

                        <div class="bars">
                            @foreach($kpis as $kpi)
                                <div class="bar">
                                    <span class="title">{{ $kpi['title']['ar'] ?? 'بدون عنوان' }}</span>

                                    <div class="bar-container">
                                        <div class="progress" style="width: {{ $kpi['percent'] }}%"></div>
                                    </div>

                                    <span class="rate">{{ number_format($kpi['avg_rating'], 1) }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <a href="#" class="ad-banner" data-bs-toggle="modal" data-bs-target="#adModal">
                        <img src="{{ asset('assets/images/mobile-banner.png') }}" alt="banner">
                    </a>
                </div>

                <div class="col-md-3">
                    <div class="reviews">
                        @foreach($reviews as $review)
                            <div class="review-item">
                                <div class="reviewer">
                                    <img src="https://static.vecteezy.com/system/resources/previews/028/287/384/non_2x/a-mature-indian-male-doctor-on-a-white-background-ai-generated-photo.jpg" alt="مراجعة">

                                    <div class="reviewer-details">
                                        <h5> {{ $review->user->localizedName }} </h5>

                                        <div class="rate">
                                            <div class="stars">
                                                @for ($i = 0; $i < floor($review->avg_score); $i++)
                                                    <svg width="14" height="13" viewBox="0 0 14 13" fill="#F6C000" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6.52447 0.463524C6.67415 0.00286841 7.32585 0.00286996 7.47553 0.463525L8.68386 4.18237C8.75079 4.38838 8.94277 4.52786 9.15938 4.52786H13.0696C13.554 4.52786 13.7554 5.14767 13.3635 5.43237L10.2001 7.73075C10.0248 7.85807 9.95149 8.08375 10.0184 8.28976L11.2268 12.0086C11.3764 12.4693 10.8492 12.8523 10.4573 12.5676L7.29389 10.2693C7.11865 10.1419 6.88135 10.1419 6.70611 10.2693L3.54267 12.5676C3.15081 12.8523 2.62357 12.4693 2.77325 12.0086L3.98157 8.28976C4.04851 8.08375 3.97518 7.85807 3.79994 7.73075L0.636495 5.43237C0.244639 5.14767 0.446028 4.52786 0.93039 4.52786H4.84062C5.05723 4.52786 5.24921 4.38838 5.31614 4.18237L6.52447 0.463524Z"/>
                                                    </svg>
                                                @endfor
                                            </div>

                                            <span>
                                                {{ \Carbon\Carbon::parse($review->updated_at)->diffForHumans() }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <p>
                                    {{ $review->comment }}
                                </p>
                            </div>
                        @endforeach

                        <!-- Pagination Links -->
                        <div class="pagination-links">
                            {{ $reviews->links() }}
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="recommended-doctors">
                        <h2>
                            مختصون آخرون يمكنك استشارتهم

                            <img src="{{ asset('assets/images/hero-shape1.png') }}" alt="Hero Shape">
                        </h2>

                        <div class="row">
                            @foreach($similarDoctors as $similarDoctor)
                                <div class="col-md-4">
                                    <x-doctor-card :doctor="$similarDoctor"/>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
