@extends('layouts.app')

@section('title', 'ابحث عن افضل دكتور في السعودية')

@section('schema')
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "قيم دكتوري",
            "url": "https://qayimdactory.com/",
            "logo": "{{ asset('assets/images/logo.png') }}",
        "sameAs": [
        @php
            $socialLinks = [
                'facebook' => $socialLoginLinks->facebook_link ?? null,
                'twitter' => $socialLoginLinks->twitter_link ?? null,
                'linkedin' => $socialLoginLinks->linkedin_link ?? null,
                'instagram' => $socialLoginLinks->instagram_link ?? null,
                'youtube' => $socialLoginLinks->youtube_link ?? null,
            ];
            $filteredLinks = array_filter($socialLinks); // Remove null values
            echo json_encode(array_values($filteredLinks), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        @endphp
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+966123456789",
            "contactType": "customer support"
        },
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://qayimdactory.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "hasPart": [
            {
                "@type": "WebPage",
                "name": "الأطباء الأكثر تقييماً",
                "url": "https://qayimdactory.com/top-rated-doctors"
            },
            {
                "@type": "WebPage",
                "name": "كيف تستفيد من خدماتنا",
                "url": "https://qayimdactory.com/how-to-use"
            },
            {
                "@type": "WebPage",
                "name": "منصة كلام في الصحة",
                "url": "https://qayimdactory.com/health-platform"
            },
            {
                "@type": "WebPage",
                "name": "اتصل بنا",
                "url": "https://qayimdactory.com/contact"
            },
            {
                "@type": "WebPage",
                "name": "سياسة الخصوصية",
                "url": "https://qayimdactory.com/privacy-policy"
            },
            {
                "@type": "WebPage",
                "name": "الشروط والأحكام",
                "url": "https://qayimdactory.com/terms"
            }
        ],
        "department": [
            {
                "@type": "MedicalClinic",
                "name": "أمراض القلب",
                "medicalSpecialty": "Cardiology",
                "url": "https://qayimdactory.com/cardiology"
            },
            {
                "@type": "MedicalClinic",
                "name": "الأسنان",
                "medicalSpecialty": "Dentistry",
                "url": "https://qayimdactory.com/dentistry"
            },
            {
                "@type": "MedicalClinic",
                "name": "الجراحة العامة",
                "medicalSpecialty": "GeneralSurgery",
                "url": "https://qayimdactory.com/general-surgery"
            },
            {
                "@type": "MedicalClinic",
                "name": "أمراض العيون",
                "medicalSpecialty": "Ophthalmology",
                "url": "https://qayimdactory.com/ophthalmology"
            }
        ],
        "hasMobileApplication": [
            {
                "@type": "MobileApplication",
                "name": "تطبيق قيم دكتوري - Android",
                "operatingSystem": "Android",
                "applicationCategory": "MedicalApplication",
                "downloadUrl": "https://play.google.com/store/apps/details?id=com.qayimdactory"
            },
            {
                "@type": "MobileApplication",
                "name": "تطبيق قيم دكتوري - iOS",
                "operatingSystem": "iOS",
                "applicationCategory": "MedicalApplication",
                "downloadUrl": "https://apps.apple.com/app/id123456789"
            }
        ]
    }
    </script>
@endsection

@section('content')
    @include('home.hero')

    @include('home.achievements')

    @include('home.categories')

    @include('home.top-rated-doctors')

    <div class="container">
        <a href="#" class="ad-banner" data-bs-toggle="modal" data-bs-target="#adModal">
            <img src="{{ asset('assets/images/banner.png') }}" alt="banner" class="d-md-block d-none">
            <img src="{{ asset('assets/images/mobile-banner.png') }}" alt="banner" class="d-md-none">
        </a>
    </div>

    @include('home.our-experience')

    @include('home.how-to-benefit')
@endsection
