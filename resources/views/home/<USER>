<div class="container">
    <section class="achievements">
        <div class="title">
            <h2>
                إنجازاتنا بالأرقام

                <img src="{{ asset('assets/images/hero-shape1.png') }}" alt="Hero Shape">
            </h2>
        </div>

        <div class="items">
            <div class="item">
                <h4>
                    <span class="counter" data-target="{{ $reviews }}">0</span>
                </h4>

                <h3>
                    المراجعــــــــــــــات
                </h3>
            </div>

            <div class="item">
                <h4>
                    <span class="counter" data-target="{{ $clinicCount }}">0</span>
                </h4>

                <h3>
                    العيــــــــــــادات
                </h3>
            </div>

            <div class="item">
                <h4>
                    <span class="counter" data-target="{{ $doctorCount }}">0</span>
                </h4>

                <h3>
                    الأطباء المسجلون
                </h3>
            </div>

            <div class="item">
                <h4>
                    <span class="counter" data-target="{{ $userCount }}">0</span>
                </h4>

                <h3>
                    الزوار المسجلون
                </h3>
            </div>
        </div>
    </section>
</div>

<script>
    function formatNumber(num) {
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    function initCounter() {
        const counters = document.querySelectorAll('.counter');

        if (counters.length === 0) {
            return; 
        }

        const speed = 50; // Lower number = faster animation
        const duration = 2000; // Total duration in milliseconds

        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-target'));
            const currentText = counter.innerText;
            const currentNum = parseFloat(currentText.replace('K', '')) * (currentText.includes('K') ? 1000 : 1);
            const increment = target / (duration / speed);

            if (currentNum < target) {
                const newNum = Math.ceil(currentNum + increment);
                counter.innerText = formatNumber(newNum);
                setTimeout(() => animateCounter(counter), speed);
            } else {
                counter.innerText = formatNumber(target);
            }
        };

        // Start animation immediately
        counters.forEach(counter => {
            animateCounter(counter);
        });
    }

    // Try to initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCounter);
    } else {
        initCounter();
    }
</script>
