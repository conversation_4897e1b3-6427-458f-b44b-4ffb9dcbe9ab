@extends('layouts.app')

@section('styles')
    @vite('resources/css/institutes-show.scss')
@endsection

@section('schema')
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "MedicalOrganization",
            "name": "{{ $institute->localizedName }}",
            "description": "{{ strip_tags($institute->localizedDescription) ?? 'Medical facility providing healthcare services' }}",
            "image": "{{ asset('assets/images/institute-bg.jpeg') }}",
            "url": "{{ route('institutes.show', ['institute' => $institute->id, 'slug' => $institute->slug]) }}",
            "address": {
                "@type": "PostalAddress",
                "addressCountry": "{{ optional($institute->country)->localizedTitle }}",
                "addressRegion": "{{ optional($institute->state)->localizedTitle }}",
                "addressLocality": "{{ optional($institute->city)->localizedTitle }}"
            },
            "telephone": "{{ $institute->phone }}",
            "email": "{{ $institute->institute_email }}",
            "medicalSpecialty": [
                @if($institute->departments && count($institute->departments) > 0)
                    @foreach($institute->departments as $department)
                        {
                            "@type": "MedicalSpecialty",
                            "name": "{{ $department->localizedTitle }}"
                        }@if(!$loop->last),@endif
                    @endforeach
                @endif
            ],
            "employee": [
                @if($doctors && count($doctors) > 0)
                    @foreach($doctors as $doctor)
                        {
                            "@type": "Person",
                            "name": "{{ $doctor->full_name_ar }}",
                            "jobTitle": "{{ optional($doctor->specialty)->localizedTitle ?? 'Not specified' }}",
                            "image": "{{ $doctor->profile_photo_url }}",
                            "url": "{{ route('doctors.show', ['doctor' => $doctor->id, 'slug' => $doctor->slug]) }}"
                        }@if(!$loop->last),@endif
                    @endforeach
                @endif
            ],
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "{{ $institute->avg_rating ?? '0' }}",
                "ratingCount": "{{ $institute->ratings_count ?? '0' }}",
                "bestRating": "5",
                "worstRating": "1"
            }
        }
    </script>
@endsection

@section('meta_description')
    {{ strip_tags($institute->localizedDescription) ?? 'Medical facility providing healthcare services' }}
@endsection

@section('title')
    {{ $institute->localizedName }}
@endsection

@section('content')
    @include('partials.breadcrumbs', ['breadcrumbs' => [
        ['name' => 'الرئيسية', 'url' => route('home')],
        ['name' => 'المنشأة الطبية']
    ]])

    <div class="container">
        <div class="institutes-page">
            <header class="page-header" style="background-image: url('{{ asset('assets/images/institute-bg.jpeg') }}');">
                <div class="institutes-details">
                    <h1> {{ $institute->localizedName }} </h1>

                    <div class="item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4" d="M16.24 2H7.76C5 2 4 3 4 5.81V18.19C4 21 5 22 7.76 22H16.23C19 22 20 21 20 18.19V5.81C20 3 19 2 16.24 2Z" fill="#102841"/>
                            <path d="M14 6.25H10C9.59 6.25 9.25 5.91 9.25 5.5C9.25 5.09 9.59 4.75 10 4.75H14C14.41 4.75 14.75 5.09 14.75 5.5C14.75 5.91 14.41 6.25 14 6.25Z" fill="#102841"/>
                            <path d="M12 19.3C12.9665 19.3 13.75 18.5165 13.75 17.55C13.75 16.5835 12.9665 15.8 12 15.8C11.0335 15.8 10.25 16.5835 10.25 17.55C10.25 18.5165 11.0335 19.3 12 19.3Z" fill="#102841"/>
                        </svg>

                        <span> +966 126677099 </span>
                    </div>

                    <div class="item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4" d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z" fill="#102841"/>
                            <path d="M12 12.87C11.16 12.87 10.31 12.61 9.66003 12.08L6.53002 9.58C6.21002 9.32 6.15003 8.85 6.41003 8.53C6.67003 8.21 7.14003 8.15 7.46003 8.41L10.59 10.91C11.35 11.52 12.64 11.52 13.4 10.91L16.53 8.41C16.85 8.15 17.33 8.2 17.58 8.53C17.84 8.85 17.79 9.33 17.46 9.58L14.33 12.08C13.69 12.61 12.84 12.87 12 12.87Z" fill="#102841"/>
                        </svg>

                        <span> {{ $institute->institute_email ?? 'Not provided' }} </span>
                    </div>

                    <div class="item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4" d="M20.62 8.45C19.57 3.83 15.54 1.75 12 1.75C12 1.75 12 1.75 11.99 1.75C8.45997 1.75 4.41997 3.82 3.36997 8.44C2.19997 13.6 5.35997 17.97 8.21997 20.72C9.27997 21.74 10.64 22.25 12 22.25C13.36 22.25 14.72 21.74 15.77 20.72C18.63 17.97 21.79 13.61 20.62 8.45Z" fill="#102841"/>
                            <path d="M12 13.46C13.7397 13.46 15.15 12.0497 15.15 10.31C15.15 8.57028 13.7397 7.15997 12 7.15997C10.2603 7.15997 8.84998 8.57028 8.84998 10.31C8.84998 12.0497 10.2603 13.46 12 13.46Z" fill="#102841"/>
                        </svg>

                        <span>{{ optional($institute->city)->localizedTitle }}, {{ optional($institute->state)->localizedTitle }}
                                                        , {{ optional($institute->country)->localizedTitle }} </span>
                    </div>
                </div>
            </header>

            @if($institute->localizedDescription)
                <div class="mt-3">{!! $institute->localizedDescription !!}</div>
            @endif

            <div>
                <a href="#" class="ad-banner d-block mb-4" data-bs-toggle="modal" data-bs-target="#adModal">
                    <img src="{{ asset('assets/images/banner.png') }}" alt="banner" class="d-md-block d-none">
                    <img src="{{ asset('assets/images/mobile-banner.png') }}" alt="banner" class="d-md-none">
                </a>
            </div>

            <div class="doctors-list">
                <div class="row">
                    @foreach ($doctors as $doctor)
                        <div class="col-md-3">
                            <x-vertical-doctor-card :doctor="$doctor" />
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endsection
