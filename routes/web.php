<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CmsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\DoctorController;
use App\Http\Controllers\InstituteController;
use App\Http\Controllers\SitemapController;
use App\Http\Controllers\SecondOpinionController;


Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/sitemap.xml', [SitemapController::class, 'index']);
Route::get('institutes', [InstituteController::class, 'index'])->name('institutes.index');
Route::get('clinic-home/{institute}/{slug}', [InstituteController::class, 'show'])
    ->name('institutes.show');
Route::get('doctors', [DoctorController::class, 'index'])->name('doctors.index');
Route::get('doctor/{doctor}/{slug}', [DoctorController::class, 'show'])
    ->name('doctors.show');

Route::get('/cms/{slug}', [CmsController::class, 'show'])->name('cms.show');

Route::get('/', [HomeController::class, 'index'])->name('home');

Route::post('/second-opinion', [SecondOpinionController::class, 'store'])->name('second-opinion.store');
