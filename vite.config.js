import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import path from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/main.scss',
                'resources/css/doctor-show.scss',
                'resources/css/institutes-show.scss',
                'resources/css/page.scss',
                'resources/js/app.js',
                'resources/js/analytics.js'
            ],
            refresh: true,
        }),
    ],
    build: {
        chunkSizeWarningLimit: 1000,
        cssCodeSplit: true,
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                pure_funcs: ['console.log'],
                keep_fnames: true,
                passes: 2,
                global_defs: {
                    'process.env.NODE_ENV': '"production"'
                }
            },
            mangle: {
                keep_fnames: true
            },
            format: {
                comments: false
            }
        },
    },
});
