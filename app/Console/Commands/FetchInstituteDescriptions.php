<?php

namespace App\Console\Commands;

use App\Models\Institute;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class FetchInstituteDescriptions extends Command
{
    protected $signature = 'institutes:fetch-descriptions {id? : Institute id} {--force : Update even if description exists}';
    protected $description = 'Generate SEO friendly descriptions using Google Places and OpenAI';

    public function handle(): int
    {
        $googleKey = config('services.google.places_key');
        $openaiKey = config('services.openai.key');

        if (!$googleKey) {
            $this->error('GOOGLE_PLACES_API_KEY is not configured.');
            return self::FAILURE;
        }

        if (!$openaiKey) {
            $this->error('OPENAI_API_KEY is not configured.');
            return self::FAILURE;
        }

        $id = $this->argument('id');

        $query = Institute::query();
        if ($id) {
            $query->where('id', $id);
        } elseif (!$this->option('force')) {
            $query->whereNull('description');
        }

        $query->with(['city', 'country']);

        $count = $query->count();
        if ($count === 0) {
            $this->info('No institutes found.');
            return self::SUCCESS;
        }

        $bar = $this->output->createProgressBar($count);

        foreach ($query->cursor() as $institute) {
            $search = $institute->name['en'] ?? $institute->name['ar'];
            if ($institute->city) {
                $search .= ', ' . $institute->city->localizedTitle;
            }
            if ($institute->country) {
                $search .= ', ' . $institute->country->localizedTitle;
            }

            $response = Http::get('https://maps.googleapis.com/maps/api/place/textsearch/json', [
                'query' => $search,
                'key'   => $googleKey,
            ]);

            if (!$response->ok() || empty($response['results'][0]['place_id'])) {
                $this->warn("No place found for {$institute->id}: {$search}");
                $bar->advance();
                continue;
            }

            $placeId = $response['results'][0]['place_id'];
            $details = Http::get('https://maps.googleapis.com/maps/api/place/details/json', [
                'place_id' => $placeId,
                'key'      => $googleKey,
                'language' => 'en',
            ]);

            if (!$details->ok()) {
                $this->warn("Details not fetched for {$institute->id}");
                $bar->advance();
                continue;
            }

            $result = $details['result'];
            $address = $result['formatted_address'] ?? '';
            $rating = $result['rating'] ?? '';
            $website = $result['website'] ?? '';

            $prompt = "Generate a long SEO friendly HTML description in Arabic for a medical clinic. Include the clinic name, address, rating, and website if available. The description should be around three paragraphs. Clinic: {$search}. Address: {$address}. Rating: {$rating}. Website: {$website}";

            $aiResponse = Http::withToken($openaiKey)
                ->timeout(30)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o',
                    'messages' => [
                        ['role' => 'system', 'content' => 'You are a helpful assistant that writes SEO friendly clinic descriptions in Arabic.'],
                        ['role' => 'user', 'content' => $prompt],
                    ],
                    'temperature' => 0.7,
                ]);

            $arabicDesc = trim($aiResponse->json('choices.0.message.content') ?? '');

            if (!$arabicDesc) {
                $this->warn("Arabic description generation failed for {$institute->id}");
                $bar->advance();
                continue;
            }

            $aiResponse = Http::withToken($openaiKey)
                ->timeout(30)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o',
                    'messages' => [
                        ['role' => 'system', 'content' => 'Translate the following clinic description from Arabic to English. Respond with the translated text only.'],
                        ['role' => 'user', 'content' => $arabicDesc],
                    ],
                    'temperature' => 0.2,
                ]);

            $englishDesc = trim($aiResponse->json('choices.0.message.content') ?? '');

            $institute->description = [
                'ar' => $arabicDesc,
                'en' => $englishDesc,
            ];
            $institute->save();

            $bar->advance();
        }

        $bar->finish();
        $this->info("\nDescriptions updated.");

        return self::SUCCESS;
    }
}
