<?php

namespace App\Console\Commands;

use App\Helpers\ImageHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class OptimizeImages extends Command
{
    protected $signature = 'images:optimize';
    protected $description = 'Optimize all images in the public directory';

    public function handle()
    {
        $this->info('Starting image optimization...');

        $images = File::allFiles(public_path('assets/images'));
        $bar = $this->output->createProgressBar(count($images));

        foreach ($images as $image) {
            if (in_array($image->getExtension(), ['jpg', 'jpeg', 'png'])) {
                try {
                    ImageHelper::optimizeImage('assets/images/' . $image->getRelativePathname());
                    $this->info("\nOptimized: " . $image->getRelativePathname());
                } catch (\Exception $e) {
                    $this->error("\nFailed to optimize: " . $image->getRelativePathname() . " - " . $e->getMessage());
                }
            }
            $bar->advance();
        }

        $bar->finish();
        $this->info("\nImage optimization completed!");
    }
} 