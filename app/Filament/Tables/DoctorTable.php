<?php

namespace App\Filament\Tables;

use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;

class  DoctorTable
{

    public static function getTable(): array
    {
        return [
            TextColumn::make('localizedName')
                ->label(__('Name'))
                ->searchable(query: fn(Builder $query, $search) => $query->where('full_name', 'like', "%$search%"))
                ->sortable(),
            TextColumn::make('institute.localizedName')
                ->label(__('Institute'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('institute',
                    function (Builder $q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('FullPhone')
                ->label(__('Phone'))
                ->default('-------')
                ->searchable(query: function (Builder $query, string $search) {
                    $query->whereRaw("CONCAT(dialing_code, '', other_phone) LIKE ?", ["%{$search}%"]);
                }),
            TextColumn::make('gender')
                ->label(__('Gender')),
            TextColumn::make('city.localizedTitle')
                ->label(__('City'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('city',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('avg_rating')
                ->label(__('Rating'))
                ->numeric()
                ->sortable(),
            TextColumn::make('rating_count')
                ->label(__('Rating Count'))
                ->numeric()
                ->sortable(),
            TextColumn::make('rating_user_count')
                ->label(__('Rating User Count'))
                ->numeric()
                ->sortable(),
            TextColumn::make('visit_count')
                ->label(__('Visit Count'))
                ->numeric()
                ->sortable(),
            TextColumn::make('department.localizedTitle')
                ->label(__('Department'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('department',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('specialty.localizedTitle')
                ->label(__('Specialty'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('specialty',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('rank.localizedTitle')
                ->label(__('Rank'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('rank',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ),
            IconColumn::make('is_verified')
                ->label(__('Is verified'))
                ->boolean(),
            IconColumn::make('status')
                ->label(__('Status')),
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('updated_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('deleted_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }
}