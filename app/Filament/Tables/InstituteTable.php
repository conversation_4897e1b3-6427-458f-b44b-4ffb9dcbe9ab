<?php

namespace App\Filament\Tables;

use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;

class  InstituteTable
{

    public static function getTable(): array
    {
        return [
            TextColumn::make('localizedName')
                ->label(__('Institute'))
                ->searchable(query: fn(Builder $query, $search) => $query->where('name', 'like', "%$search%"))
                ->sortable(),
            TextColumn::make('institute_email')
                ->label(__('Institute Email'))
                ->default('-------')
                ->sortable()
                ->searchable(),
            TextColumn::make('FullPhone')
                ->label(__('Phone'))
                ->default('-------')
                ->searchable(query: function (Builder $query, string $search) {
                    $query->whereRaw("CONCAT(dialing_code, '', other_phone) LIKE ?", ["%{$search}%"]);
                }),
            TextColumn::make('city.localizedTitle')
                ->label(__('City'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('city',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('localizedDescription')
                ->label(__('Description'))
                ->html()
                ->limit(50)
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make("instituteType.localizedTitle")
                ->label(__('Institute Type'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('instituteType',
                    function (Builder $q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                ), IconColumn::make('is_verified')
                ->label(__('Is verified'))
                ->boolean(),
            IconColumn::make('status')
                ->label(__('Status'))
                ->sortable()->searchable(),
            TextColumn::make('created_at')
                ->label(__('Created At'))
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('updated_at')
                ->label(__('Updated At'))
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('deleted_at')
                ->label(__('Deleted At'))
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }
}