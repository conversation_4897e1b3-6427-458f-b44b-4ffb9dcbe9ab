<?php

namespace App\Filament\Tables;


use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;

class InstituteMessageTable
{


    public static function getTable(): array
    {
        return [
            TextColumn::make('institute.localizedName')
                ->label(__('Institute'))
                ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('institute',
                    function (Builder $q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                ),
            TextColumn::make('full_name')
                ->label(__('Full Name'))
                ->searchable(),
            TextColumn::make('email')
                ->label(__('Email'))
                ->searchable(),
            TextColumn::make('FullPhone')
                ->label(__('Phone'))
                ->default('-------')
                ->searchable(query: function (Builder $query, string $search) {
                    $query->whereRaw("CONCAT(dialing_code, '', phone) LIKE ?", ["%{$search}%"]);
                }),
            TextColumn::make('subject')
                ->label(__('Subject'))
                ->searchable(),
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('updated_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }
}