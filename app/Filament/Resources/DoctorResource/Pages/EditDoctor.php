<?php

namespace App\Filament\Resources\DoctorResource\Pages;

use App\Models\Department;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\DoctorResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDoctor extends EditRecord
{
    protected static string $resource = DoctorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if ($this->shouldUpdateComputedFields($data)) {
            $data['full_name'] = [
                'ar' => trim("{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['middle_name']['ar']} {$data['last_name']['ar']}"),
                'en' => trim("{$data['salutation']['en']} {$data['first_name']['en']} {$data['middle_name']['en']} {$data['last_name']['en']}"),
            ];

            $data['full_name_ar'] = "{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['middle_name']['ar']} {$data['last_name']['ar']}";

            $department = Department::findOrFail($data['department_id']);
            $specialty_ar = $department->title['ar'] ?? '';
            $specialty_en = $department->title['en'] ?? '';

            $data['profile_url'] = [
                'ar' => str_replace(' ', '-',
                    "{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['last_name']['ar']} {$specialty_ar}"),
                'en' => str_replace(' ', '-',
                    "{$data['salutation']['en']} {$data['first_name']['en']} {$data['last_name']['en']} {$specialty_en}"),
            ];
        }

        return parent::handleRecordUpdate($record, $data);
    }

    protected function shouldUpdateComputedFields(array $data): bool
    {
        $fields = ['salutation', 'first_name', 'middle_name', 'last_name'];

        foreach ($fields as $field) {
            $formValue   = (array)$data[$field];
            $recordValue = (array)$this->record->{$field};

            if ($formValue != $recordValue) {
                return true;
            }
        }

        return $data['department_id'] != $this->record->department_id;
    }
}
