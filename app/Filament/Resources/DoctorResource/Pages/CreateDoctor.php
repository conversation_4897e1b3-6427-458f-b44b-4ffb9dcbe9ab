<?php

namespace App\Filament\Resources\DoctorResource\Pages;

use App\Models\Department;
use App\Filament\Resources\DoctorResource;
use Filament\Resources\Pages\CreateRecord;

class CreateDoctor extends CreateRecord
{
    protected static string $resource = DoctorResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['full_name'] = [
            'ar' => trim("{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['middle_name']['ar']} {$data['last_name']['ar']}"),
            'en' => trim("{$data['salutation']['en']} {$data['first_name']['en']} {$data['middle_name']['en']} {$data['last_name']['en']}"),
        ];

        $data['full_name_ar'] = "{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['middle_name']['ar']} {$data['last_name']['ar']}";
        $department = Department::findOrFail($data['department_id']);

        $specialty_ar = $department ? ($department->title['ar'] ?? '') : '';
        $specialty_en = $department ? ($department->title['en'] ?? '') : '';
        $data['profile_url'] = [
            'ar' => str_replace(' ', '-', "{$data['salutation']['ar']} {$data['first_name']['ar']} {$data['last_name']['ar']} {$specialty_ar}"),
            'en' => str_replace(' ', '-', "{$data['salutation']['en']} {$data['first_name']['en']} {$data['last_name']['en']} {$specialty_en}"),
        ];

        return parent::mutateFormDataBeforeCreate($data);
    }

}
