<?php

namespace App\Filament\Resources\DoctorResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Forms\InstituteForm;
use App\Filament\Tables\InstituteTable;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstituteRelationManager extends RelationManager
{
    protected static string $relationship = 'institute';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Institute');
    }

    protected static function getPluralModelLabel(): ?string
    {
        return __('Institute');
    }

    public function getTableModelLabel(): ?string
    {
        return __('Institute');
    }

    public static function getModelLabel(): string
    {
        return __('Institute');
    }
    public static function getLabel(): ?string
    {
        return __('Institute');
    }

    protected static function getRecordLabel(): ?string
    {
        return __('Institute');

    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(InstituteForm::getSchema());
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('institute.localizedName')
            ->columns(InstituteTable::getTable())
            ->filters([
                //
            ])
            ->headerActions([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
