<?php

namespace App\Filament\Resources\SecondOpinionResource\Pages;

use App\Filament\Resources\SecondOpinionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSecondOpinion extends EditRecord
{
    protected static string $resource = SecondOpinionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
