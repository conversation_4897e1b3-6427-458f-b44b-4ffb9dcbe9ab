<?php

namespace App\Filament\Resources\SecondOpinionResource\Pages;

use App\Filament\Resources\SecondOpinionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSecondOpinions extends ListRecords
{
    protected static string $resource = SecondOpinionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
