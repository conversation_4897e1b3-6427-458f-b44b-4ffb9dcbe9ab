<?php

namespace App\Filament\Resources;

use App\Models\Country;
use App\Enums\StateStatus;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use App\Filament\Resources\StateResource\Pages;
use App\Filament\Resources\StateResource\RelationManagers;
use App\Models\State;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StateResource extends Resource
{
    protected static ?string $model = State::class;

    protected static ?string $navigationIcon = 'tabler-flag-cog';
    protected static ?int $navigationSort = 8;

    public static function getNavigationLabel(): string
    {
        return __('States');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Address Management');
    }

    public static function getLabel(): ?string
    {
        return __('States');
    }

    public static function getPluralLabel(): ?string
    {
        return __('States');
    }

    public static function getModelLabel(): string
    {
        return __('States');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('Title'))
                            ->required()
                            ->translatable(),
                        Select::make('country_code')
                            ->label(__('Country'))
                            ->options(fn() => Country::all()->pluck('localizedTitle', 'code'))
                            ->reactive()
                            ->searchable()
                            ->preload(20)
                            ->required(),
                        Forms\Components\TextInput::make('state_code')
                            ->label(__('State Code'))
                            ->required(),
                        Forms\Components\TextInput::make('physical')
                            ->label(__('Physical Area')),
                        Forms\Components\Select::make('status')
                            ->label(__('Status'))
                            ->options(StateStatus::toArray())
                            ->required(),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('localizedTitle')
                    ->label(__('State'))
                    ->searchable(query: fn(Builder $query, $search) => $query->where('title', 'like', "%$search%"))
                    ->sortable(),
                TextColumn::make('country.localizedTitle')
                    ->label(__('Country'))
                    ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('country',
                        function (Builder $q) use ($search) {
                            $q->where('title', 'like', "%{$search}%");
                        })
                    ),
                Tables\Columns\TextColumn::make('state_code')
                    ->label(__('State Code'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('physical')
                    ->label(__('Physical Area'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListStates::route('/'),
            'create' => Pages\CreateState::route('/create'),
            'edit'   => Pages\EditState::route('/{record}/edit'),
        ];
    }
}
