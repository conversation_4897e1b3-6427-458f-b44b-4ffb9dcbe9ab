<?php

namespace App\Filament\Resources\InstituteResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Forms\DoctorForm;
use App\Filament\Tables\DoctorTable;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DoctorsRelationManager extends RelationManager
{
    protected static string $relationship = 'doctors';
    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Doctors');
    }

    protected static function getPluralModelLabel(): ?string
    {
        return __('Doctors');
    }

    public function getTableModelLabel(): ?string
    {
        return __('Doctors');
    }

    public static function getModelLabel(): string
    {
        return __('Doctors');
    }
    public static function getLabel(): ?string
    {
        return __('Doctors');
    }

    protected static function getRecordLabel(): ?string
    {
        return __('Doctors');

    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(DoctorForm::getSchema());
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('doctors.localizedName')
            ->columns(DoctorTable::getTable())
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
