<?php

namespace App\Filament\Resources\InstituteResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Forms\InstituteMessageForm;
use App\Filament\Tables\InstituteMessageTable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstituteMessagesRelationManager extends RelationManager
{
    protected static string $relationship = 'instituteMessages';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Institutes Messages');
    }

    protected static function getPluralModelLabel(): ?string
    {
        return __('Institutes Messages');
    }

    public function getTableModelLabel(): ?string
    {
        return __('Institutes Messages');
    }

    public static function getModelLabel(): string
    {
        return __('Institutes Messages');
    }
    public static function getLabel(): ?string
    {
        return __('Institutes Messages');
    }

    protected static function getRecordLabel(): ?string
    {
        return __('Institutes Messages');

    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(InstituteMessageForm::getSchema());
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('institute.localizedName')
            ->columns(InstituteMessageTable::getTable())
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
