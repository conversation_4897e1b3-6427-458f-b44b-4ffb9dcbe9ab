<?php

namespace App\Filament\Resources;

use App\Enums\CountryStatus;
use Filament\Tables\Columns\TextColumn;
use App\Filament\Resources\CountryResource\Pages;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\CountryResource\RelationManagers;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CountryResource extends Resource
{
    protected static ?string $model = Country::class;

    protected static ?string $navigationIcon = 'tabler-world';

    protected static ?int $navigationSort = 7;

    public static function getNavigationLabel(): string
    {
        return __('Country');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Address Management');
    }

    public static function getLabel(): ?string
    {
        return __('Country');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Country');
    }

    public static function getModelLabel(): string
    {
        return __('Country');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
               Forms\Components\Section::make()
                ->schema([
                    SpatieMediaLibraryFileUpload::make('flag')
                        ->collection('countries_icons')
                        ->label(__('Country Flag'))
                        ->image()
                        ->openable()
                        ->downloadable()
                        ->rules('mimes:jpeg,png,jpg')
                        ->required()
                        ->columnSpanFull(),
                    Forms\Components\TextInput::make('title')
                        ->label(__('Title'))
                        ->required()
                        ->translatable(),
                    Forms\Components\TextInput::make('capital_name')
                        ->label(__('Capital Name'))
                        ->required()
                        ->translatable(),
                    Forms\Components\TextInput::make('code')
                        ->required()
                        ->maxLength(2)
                        ->label(__('Code')),
                    Forms\Components\TextInput::make('code_iso')
                        ->label(__('Code ISO'))
                        ->required()
                        ->maxLength(3),
                    Forms\Components\TextInput::make('dialing_code')
                        ->label(__('Dialing Code'))
                        ->required()
                        ->maxLength(191),
                    Forms\Components\Select::make('status')
                        ->label(__('Status'))
                        ->options(CountryStatus::toArray())
                        ->required(),
                ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('localizedTitle')
                    ->label(__('Title'))
                    ->searchable(query: fn(Builder $query, $search) => $query->where('title', 'like', "%$search%")),

                Tables\Columns\TextColumn::make('code')
                    ->label(__('Code'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('code_iso')
                    ->label(__('Code ISO'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('dialing_code')
                    ->label(__('Dialing Code'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListCountries::route('/'),
            'create' => Pages\CreateCountry::route('/create'),
            'edit'   => Pages\EditCountry::route('/{record}/edit'),
        ];
    }
}
