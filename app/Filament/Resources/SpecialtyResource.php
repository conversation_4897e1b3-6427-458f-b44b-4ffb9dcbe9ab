<?php

namespace App\Filament\Resources;

use App\Models\Department;
use App\Enums\SpecialtyStatus;
use Filament\Forms\Components\Select;
use App\Filament\Resources\SpecialtyResource\Pages;
use App\Filament\Resources\SpecialtyResource\RelationManagers;
use App\Models\Specialty;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SpecialtyResource extends Resource
{
    protected static ?string $model = Specialty::class;

    protected static ?string $navigationIcon = 'tabler-adjustments-star';
    protected static ?int $navigationSort =5;

    public static function getNavigationLabel(): string
    {
        return __('Specialty');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Management');
    }

    public static function getLabel(): ?string
    {
        return __('Specialty');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Specialty');
    }

    public static function getModelLabel(): string
    {
        return __('Specialty');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->label(__('Title'))->translatable(),
                    Select::make('department_id')
                        ->required()
                        ->label(__('Department'))
                        ->searchable()
                        ->preload(20)
                        ->options(Department::all()->pluck('localizedTitle', 'id')),

                    Forms\Components\Select::make('status')
                        ->label(__('Status'))
                        ->options(SpecialtyStatus::toArray())
                        ->required(),
                ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('department.localizedTitle')
                    ->label(__('Department'))
                    ->default('-----')
                    ->searchable(query: fn(Builder $query, $search) => $query->orWhereHas('department',
                        function (Builder $q) use ($search) {
                            $q->where('title', 'like', "%{$search}%");
                        })
                    ),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListSpecialties::route('/'),
            'create' => Pages\CreateSpecialty::route('/create'),
            'edit'   => Pages\EditSpecialty::route('/{record}/edit'),
        ];
    }
}
