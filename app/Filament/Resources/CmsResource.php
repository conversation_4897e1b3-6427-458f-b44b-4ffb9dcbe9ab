<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CmsResource\Pages;
use App\Models\Cms;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CmsResource extends Resource
{
    protected static ?string $model = Cms::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Content Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('English')
                            ->schema([
                                Forms\Components\TextInput::make('title.en')
                                    ->label('Title (English)')
                                    ->required(),
                                Forms\Components\RichEditor::make('content.en')
                                    ->label('Content (English)')
                                    ->required(),
                                Forms\Components\TextInput::make('meta_keywords.en')
                                    ->label('Meta Keywords (English)'),
                                Forms\Components\Textarea::make('meta_description.en')
                                    ->label('Meta Description (English)'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Arabic')
                            ->schema([
                                Forms\Components\TextInput::make('title.ar')
                                    ->label('Title (Arabic)')
                                    ->required(),
                                Forms\Components\RichEditor::make('content.ar')
                                    ->label('Content (Arabic)')
                                    ->required(),
                                Forms\Components\TextInput::make('meta_keywords.ar')
                                    ->label('Meta Keywords (Arabic)'),
                                Forms\Components\Textarea::make('meta_description.ar')
                                    ->label('Meta Description (Arabic)'),
                            ]),
                    ]) ->columnSpan('full'),
               
                    Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Select::make('position')
                    ->options([
                        'Header' => 'Header',
                        'Footer' => 'Footer',
                        'Sidebar' => 'Sidebar',
                        'None' => 'None',
                    ])
                    ->required(),
                    Forms\Components\TextInput::make('slug')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\FileUpload::make('banner_image')
                    ->image()
                    ->directory('cms/banners'),
                Forms\Components\TextInput::make('sort')
                    ->numeric()
                    ->default(0),
                Forms\Components\Select::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ])
                    ->required(),
                ])->columns(2),
              
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('localizedTitle')
                    ->label('Title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('position')
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sort')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('position')
                    ->options([
                        'Header' => 'Header',
                        'Footer' => 'Footer',
                        'Sidebar' => 'Sidebar',
                        'None' => 'None',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCms::route('/'),
            'create' => Pages\CreateCms::route('/create'),
            'edit' => Pages\EditCms::route('/{record}/edit'),
        ];
    }
} 