<?php

namespace App\Filament\Resources;

use App\Enums\DepartmentStatus;
use Filament\Tables\Columns\TextColumn;
use App\Filament\Resources\DepartmentResource\Pages;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\DepartmentResource\RelationManagers;
use App\Models\Department;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;


class DepartmentResource extends Resource
{
    protected static ?string $model = Department::class;

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationIcon = 'tabler-device-ipad-horizontal-cog';

    public static function getNavigationLabel(): string
    {
        return __('Department');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Management');
    }

    public static function getLabel(): ?string
    {
        return __('Department');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Department');
    }

    public static function getModelLabel(): string
    {
        return __('Department');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('icon')
                            ->collection('departments_icons')
                            ->label(__('Icon'))
                            ->image()
                            ->openable()
                            ->downloadable()
                            ->rules('mimes:jpeg,png,jpg,svg')
                            ->required()
                        ->columnSpanFull(),
                        Forms\Components\TextInput::make('title')
                            ->label(__('Title'))
                            ->required()
                            ->translatable(),
                        Forms\Components\TextInput::make('st_id')
                            ->label(__('SehaTalk Category Id'))
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->label(__('Status'))
                            ->options(DepartmentStatus::toArray())
                            ->required(),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('localizedTitle')
                    ->label(__('Title'))
                    ->searchable(query: fn(Builder $query, $search) => $query->where('title', 'like', "%$search%"))
                    ->sortable(),
                Tables\Columns\TextColumn::make('st_id')
                    ->label(__('SehaTalk Category Id'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])->reorderable('sort')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListDepartments::route('/'),
            'create' => Pages\CreateDepartment::route('/create'),
            'edit'   => Pages\EditDepartment::route('/{record}/edit'),
        ];
    }
}
