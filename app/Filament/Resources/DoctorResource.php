<?php

namespace App\Filament\Resources;


use App\Models\City;
use App\Models\State;
use App\Models\Country;
use App\Models\Institute;
use App\Models\Specialty;
use App\Models\Department;
use App\Enums\DoctorStatus;
use App\Models\Subspecialty;
use App\Models\InstituteType;
use App\Enums\InstituteStatus;
use App\Filament\Forms\DoctorForm;
use Filament\Tables\Filters\Filter;
use App\Filament\Tables\DoctorTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\DoctorResource\Pages;
use App\Models\Doctor;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Resources\DoctorResource\RelationManagers\InstituteRelationManager;

class DoctorResource extends Resource
{
    protected static ?string $model = Doctor::class;

    protected static ?string $navigationIcon = 'tabler-ambulance';


    public static function getNavigationLabel(): string
    {
        return __('Doctors');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Doctors Management');
    }

    public static function getLabel(): ?string
    {
        return __('Doctors');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Doctors');
    }

    public static function getModelLabel(): string
    {
        return __('Doctors');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema(DoctorForm::getSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(DoctorTable::getTable())
            ->filters([
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(DoctorStatus::toArray()),
                SelectFilter::make('department_id')
                    ->options(fn() => Department::all()->pluck('localizedTitle', 'id'))
                    ->label(__('Department')),
                SelectFilter::make('specialty_id')
                    ->options(fn() => Specialty::all()->pluck('localizedTitle', 'id'))
                    ->label(__('Specialty')),
                SelectFilter::make('subspecialty_id')
                    ->options(fn() => Subspecialty::all()->pluck('localizedTitle', 'id'))
                    ->label(__('SubSpecialty')),
                SelectFilter::make('institute_id')
                    ->options(fn() => Institute::all()->pluck('localizedName', 'id'))
                    ->label(__('Institute')),
                SelectFilter::make('country_code')
                    ->options(fn() => Country::all()->pluck('localizedTitle', 'code'))
                    ->label(__('Country')),
                SelectFilter::make('state_code')
                    ->options(fn() => State::all()->pluck('localizedTitle', 'code'))
                    ->label(__('State')),
                SelectFilter::make('city_id')
                    ->options(fn() => City::all()->pluck('localizedTitle', 'id'))
                    ->label(__('City')),
                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->form([
                        DatePicker::make('created_from')
                            ->label(__('Created From')),
                        DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data) {
                        if ($data['created_from']) {
                            $query->whereDate('created_at', '>=', $data['created_from']);
                        }
                        if ($data['created_until']) {
                            $query->whereDate('created_at', '<=', $data['created_until']);
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InstituteRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListDoctors::route('/'),
            'create' => Pages\CreateDoctor::route('/create'),
            'edit'   => Pages\EditDoctor::route('/{record}/edit'),
        ];
    }
}
