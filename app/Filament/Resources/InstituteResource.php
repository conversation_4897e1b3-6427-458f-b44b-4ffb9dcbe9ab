<?php

namespace App\Filament\Resources;

use App\Models\City;
use App\Models\State;
use App\Models\Country;
use Filament\Forms\Get;
use App\Models\Category;
use App\Models\InstituteType;
use App\Enums\InstituteStatus;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;
use App\Filament\Forms\InstituteForm;
use App\Filament\Tables\InstituteTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\SelectFilter;
use App\Filament\Resources\InstituteResource\Pages;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\InstituteResource\RelationManagers;
use App\Models\Institute;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Resources\InstituteResource\RelationManagers\DoctorsRelationManager;
use App\Filament\Resources\InstituteResource\RelationManagers\InstituteMessagesRelationManager;

class InstituteResource extends Resource
{
    protected static ?string $model = Institute::class;

    protected static ?string $navigationIcon = 'tabler-building';

    public static function getNavigationLabel(): string
    {
        return __('Institutes');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Institutes Management');
    }

    public static function getLabel(): ?string
    {
        return __('Institutes');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Institutes');
    }

    public static function getModelLabel(): string
    {
        return __('Institutes');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema(InstituteForm::getSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(InstituteTable::getTable())
            ->filters([
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(InstituteStatus::toArray()),
                SelectFilter::make('is_verified')
                    ->label(__('Is verified'))
                    ->options([
                        1 => __('Verified'),
                        0 => __('Not Verified'),
                    ]),
                SelectFilter::make('institute_type_id')
                    ->options(fn() => InstituteType::all()->pluck('localizedTitle', 'id'))
                    ->label(__('Institute Type')),
                SelectFilter::make('country_code')
                    ->options(fn() => Country::all()->pluck('localizedTitle', 'id'))
                    ->label(__('Country')),
                SelectFilter::make('state_code')
                    ->options(fn() => State::all()->pluck('localizedTitle', 'id'))
                    ->label(__('State')),
                SelectFilter::make('city_id')
                    ->options(fn() => City::all()->pluck('localizedTitle', 'id'))
                    ->label(__('City')),
                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label(__('Created From')),
                        Forms\Components\DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data) {
                        if ($data['created_from']) {
                            $query->whereDate('created_at', '>=', $data['created_from']);
                        }
                        if ($data['created_until']) {
                            $query->whereDate('created_at', '<=', $data['created_until']);
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InstituteMessagesRelationManager::class,
            DoctorsRelationManager::class,

        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListInstitutes::route('/'),
            'create' => Pages\CreateInstitute::route('/create'),
            'edit'   => Pages\EditInstitute::route('/{record}/edit'),
        ];
    }
}
