<?php

namespace App\Filament\Resources;

use App\Enums\SubSpecialtyStatus;
use Filament\Tables\Columns\TextColumn;
use App\Filament\Resources\SubspecialtyResource\Pages;
use App\Filament\Resources\SubspecialtyResource\RelationManagers;
use App\Models\Subspecialty;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubspecialtyResource extends Resource
{
    protected static ?string $model = Subspecialty::class;

    protected static ?string $navigationIcon = 'tabler-briefcase';
    protected static ?int $navigationSort = 6;

    public static function getNavigationLabel(): string
    {
        return __('SubSpecialty');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Management');
    }

    public static function getLabel(): ?string
    {
        return __('SubSpecialty');
    }

    public static function getPluralLabel(): ?string
    {
        return __('SubSpecialty');
    }

    public static function getModelLabel(): string
    {
        return __('SubSpecialty');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('Title'))->translatable(),
                        Forms\Components\Select::make('status')
                            ->label(__('Status'))
                            ->options(SubSpecialtyStatus::toArray())
                            ->required(),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('localizedTitle')
                    ->label(__('Title'))
                    ->searchable(query: fn(Builder $query, $search) => $query->where('title', 'like', "%$search%")),
                Tables\Columns\IconColumn::make('status')->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListSubspecialties::route('/'),
            'create' => Pages\CreateSubspecialty::route('/create'),
            'edit'   => Pages\EditSubspecialty::route('/{record}/edit'),
        ];
    }
}
