<?php

namespace App\Filament\Resources;


use App\Filament\Forms\InstituteMessageForm;
use App\Filament\Tables\InstituteMessageTable;
use App\Filament\Resources\InstituteMessageResource\Pages;
use App\Filament\Resources\InstituteMessageResource\RelationManagers;
use App\Models\InstituteMessage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InstituteMessageResource extends Resource
{
    protected static ?string $model = InstituteMessage::class;
    protected static ?string $navigationIcon = 'tabler-message';

    protected static ?int $navigationSort = 3;

    public static function getNavigationLabel(): string
    {
        return __('Institutes Messages');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Institutes Management');
    }

    public static function getLabel(): ?string
    {
        return __('Institutes Messages');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Institutes Messages');
    }

    public static function getModelLabel(): string
    {
        return __('Institutes Messages');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(InstituteMessageForm::getSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(InstituteMessageTable::getTable())
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListInstituteMessages::route('/'),
            'create' => Pages\CreateInstituteMessage::route('/create'),
            'edit'   => Pages\EditInstituteMessage::route('/{record}/edit'),
        ];
    }
}
