<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SecondOpinionResource\Pages;
use App\Filament\Resources\SecondOpinionResource\RelationManagers;
use App\Models\SecondOpinion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SecondOpinionResource extends Resource
{
    protected static ?string $model = SecondOpinion::class;

    protected static ?string $navigationIcon = 'heroicon-o-phone';

    protected static ?string $navigationLabel = 'الرأي الثاني';

    protected static ?string $modelLabel = 'الرأي الثاني';

    protected static ?string $pluralModelLabel = 'الآراء الثانية';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('phone_number')
                    ->label('رقم الهاتف')
                    ->required()
                    ->tel(),
                Forms\Components\Select::make('status')
                    ->label('الحالة')
                    ->options([
                        'pending' => 'قيد الانتظار',
                        'contacted' => 'تم التواصل',
                        'completed' => 'مكتمل',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('phone_number')
                    ->label('رقم الهاتف')
                    ->searchable(),
                Tables\Columns\SelectColumn::make('status')
                    ->label('الحالة')
                    ->options([
                        'pending' => 'قيد الانتظار',
                        'contacted' => 'تم التواصل',
                        'completed' => 'مكتمل',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ التسجيل')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSecondOpinions::route('/'),
            'create' => Pages\CreateSecondOpinion::route('/create'),
            'edit' => Pages\EditSecondOpinion::route('/{record}/edit'),
        ];
    }
}
