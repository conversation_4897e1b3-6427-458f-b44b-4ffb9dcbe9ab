<?php

namespace App\Filament\Resources;

use App\Enums\InstituteStatus;
use App\Enums\InstituteTypeStatus;
use Filament\Tables\Filters\SelectFilter;
use App\Filament\Resources\InstituteTypeResource\Pages;
use App\Filament\Resources\InstituteTypeResource\RelationManagers;
use App\Models\InstituteType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstituteTypeResource extends Resource
{
    protected static ?string $model = InstituteType::class;

    protected static ?string $navigationIcon = 'tabler-home-cog';

    public static function getNavigationLabel(): string
    {
        return __('Institutes Type');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Institutes Management');
    }

    public static function getLabel(): ?string
    {
        return __('Institutes Type');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Institutes Type');
    }

    public static function getModelLabel(): string
    {
        return __('Institutes Type');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('Title'))
                            ->required()
                            ->translatable(),
                        Forms\Components\Select::make('status')
                            ->label(__('Status'))
                            ->options(InstituteTypeStatus::toArray())
                            ->required(),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('localizedTitle')
                    ->label(__('Institute Type'))
                    ->searchable(query: fn(Builder $query, $search) => $query->where('title', 'like', "%{$search}%")
                    ),
                Tables\Columns\IconColumn::make('status')->label(__('Status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(InstituteTypeStatus::toArray()),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListInstituteTypes::route('/'),
            'create' => Pages\CreateInstituteType::route('/create'),
            'edit'   => Pages\EditInstituteType::route('/{record}/edit'),
        ];
    }
}
