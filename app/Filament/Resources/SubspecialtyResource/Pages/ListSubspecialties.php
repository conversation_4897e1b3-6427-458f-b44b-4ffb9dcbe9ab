<?php

namespace App\Filament\Resources\SubspecialtyResource\Pages;

use App\Filament\Resources\SubspecialtyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSubspecialties extends ListRecords
{
    protected static string $resource = SubspecialtyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
