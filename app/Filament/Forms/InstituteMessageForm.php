<?php

namespace App\Filament\Forms;

use Filament\Forms\Form;
use App\Models\Institute;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;

class InstituteMessageForm
{

    public static function getSchema(Form $form = null):array
    {
        return [
            Section::make()
                ->schema([
                    Select::make('institute_id')
                        ->required()
                        ->label(__('Institute'))
                        ->searchable()
                        ->preload(20)
                        ->options(Institute::all()->pluck('localizedName', 'id')),
                    TextInput::make('full_name')
                        ->required()
                        ->label(__('Full Name')),
                    TextInput::make('email')
                        ->email()
                        ->required()
                        ->label(__('Email')),
                    TextInput::make('dialing_code')
                        ->default('+966')
                        ->disabled()
                        ->label(__('Dialing Code')),
                    TextInput::make('phone')
                        ->tel()
                        ->required()
                        ->label(__('Phone')),
                    TextInput::make('subject')
                        ->required()
                        ->label(__('Subject')),
                    Textarea::make('message')
                        ->required()
                        ->label(__('Message'))
                        ->columnSpanFull(),
                ])->columns(2)
        ];

    }
}