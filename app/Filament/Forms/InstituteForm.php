<?php

namespace App\Filament\Forms;

use App\Models\City;
use App\Models\Rank;
use App\Models\State;
use App\Models\Country;
use Filament\Forms\Get;
use App\Models\Institute;
use App\Models\Specialty;
use App\Models\Department;
use App\Enums\DoctorStatus;
use App\Models\Subspecialty;
use App\Models\InstituteType;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class InstituteForm
{
    public static function getSchema(): array
    {
        return [
           Section::make()
                ->schema([
                    SpatieMediaLibraryFileUpload::make('logo')
                        ->collection('institutes_logo')
                        ->label(__('Logo'))
                        ->image()
                        ->openable()
                        ->downloadable()
                        ->rules('mimes:jpeg,png,jpg')
                        ->required(),
                    SpatieMediaLibraryFileUpload::make('banner')
                        ->collection('institutes_banner')
                        ->label(__('Banner'))
                        ->image()
                        ->openable()
                        ->downloadable()
                        ->rules('mimes:jpeg,png,jpg')
                        ->required(),
                    \Filament\Forms\Components\Textarea::make('description')
                        ->label(__('Description'))
                        ->translatable()
                        ->columnSpanFull(),
                   TextInput::make('name')
                        ->label(__('Name'))
                        ->required()
                        ->translatable(),
                   TextInput::make('institute_email')
                        ->label(__('Institute Email'))
                        ->required()
                        ->email(),
                   TextInput::make('dialing_code')
                        ->label(__('Dialing Code'))
                        ->disabled()
                        ->default('+966'),
                   TextInput::make('other_phone')
                        ->label(__('Phone'))
                        ->tel()
                        ->required(),
                   TextInput::make('ext')
                        ->label(__('Ext')),
                   Select::make('institute_type_id')
                        ->options(fn() => InstituteType::all()->pluck('localizedTitle', 'id'))
                        ->required()
                        ->label(__('Institute Type')),
                    Select::make('country_code')
                        ->label(__('Country'))
                        ->options(fn() => Country::all()->pluck('localizedTitle', 'code'))
                        ->reactive()
                        ->searchable()
                        ->preload(20)
                        ->required()
                        ->afterStateUpdated(fn($state, callable $set) => $set('state_code', null)),
                    Select::make('state_code')
                        ->label(__('State'))
                        ->options(fn(Get $get) => State::where('country_code', $get('country_code'))
                            ->get()
                            ->pluck('localizedTitle', 'state_code'))
                        ->reactive()
                        ->searchable()
                        ->preload(20)
                        ->afterStateUpdated(fn($state, callable $set) => $set('city_id', null))
                        ->required(),
                    Select::make('city_id')
                        ->label(__('City'))
                        ->options(fn(Get $get) => City::where('state_code', $get('state_code'))
                            ->get()
                            ->pluck('localizedTitle', 'id'))
                        ->searchable()
                        ->preload(20)
                        ->required(),
                   TextInput::make('latitude')
                        ->numeric()
                        ->label(__('Latitude')),
                   TextInput::make('longitude')
                        ->numeric()
                        ->label(__('Longitude')),

                ])->columns(2)
        ];
    }
}