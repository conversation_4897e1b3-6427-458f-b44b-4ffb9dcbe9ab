<?php

namespace App\Filament\Forms;

use App\Models\City;
use App\Models\Rank;
use App\Models\State;
use App\Models\Country;
use Filament\Forms\Get;
use App\Models\Institute;
use App\Models\Specialty;
use App\Models\Department;
use App\Enums\DoctorStatus;
use App\Models\Subspecialty;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class DoctorForm
{
    public static function getSchema(): array
    {
        return [
            Section::make(__('Doctor Information'),)
                ->schema([
                    TextInput::make('salutation')
                        ->label(__('Salutation'))
                        ->required()
                        ->translatable(),
                    TextInput::make('first_name')
                        ->label(__('First Name'))
                        ->required()
                        ->translatable(),
                    TextInput::make('middle_name')
                        ->label(__('Middle Name'))
                        ->required()
                        ->translatable(),
                    TextInput::make('last_name')
                        ->label(__('Last Name'))
                        ->required()
                        ->translatable(),
                    TextInput::make('dialing_code')
                        ->label(__('Dialing Code'))
                        ->disabled()
                        ->default('+966'),
                    TextInput::make('other_phone')
                        ->label(__('Phone'))
                        ->tel()
                        ->required(),
                    TextInput::make('ext')
                        ->label(__('Ext')),
                    DatePicker::make('dob')
                        ->label(__('Date of Birth'))
                        ->required(),
                    Select::make('gender')
                        ->label(__('Gender'))
                        ->options([
                            'male'   => __('Male'),
                            'female' => __('Female'),
                        ])
                        ->required(),
                    SpatieMediaLibraryFileUpload::make('profile_photo')
                        ->collection('doctors_images')
                        ->label(__('Profile Photo'))
                        ->image()
                        ->openable()
                        ->downloadable()
                        ->rules('mimes:jpeg,png,jpg')
                        ->required(),
                    Select::make('status')
                        ->options(DoctorStatus::toArray())
                        ->label(__('Status'))
                        ->required(),

                ])->columns(2),

            Section::make(__('Address'))
                ->schema([
                    Select::make('country_code')
                        ->label(__('Country'))
                        ->options(fn() => Country::all()->pluck('localizedTitle', 'code'))
                        ->reactive()
                        ->searchable()
                        ->preload(20)
                        ->required()
                        ->afterStateUpdated(fn($state, callable $set) => $set('state_code', null)),
                    Select::make('state_code')
                        ->label(__('State'))
                        ->options(fn(Get $get) => State::where('country_code', $get('country_code'))
                            ->get()
                            ->pluck('localizedTitle', 'state_code'))
                        ->reactive()
                        ->searchable()
                        ->preload(20)
                        ->afterStateUpdated(fn($state, callable $set) => $set('city_id', null))
                        ->required(),
                    Select::make('city_id')
                        ->label(__('City'))
                        ->options(fn(Get $get) => City::where('state_code', $get('state_code'))
                            ->get()
                            ->pluck('localizedTitle', 'id'))
                        ->searchable()
                        ->preload(20)
                        ->required(),
                ])->columns(2),

            Section::make(__('Comments & Rating'))
                ->schema([
                    TextInput::make('avg_rating')
                        ->label(__('Rating'))
                        ->numeric()
                        ->default(0.00),
                    TextInput::make('rating_count')
                        ->label(__('Rating Count'))
                        ->required()
                        ->numeric()
                        ->default(0),
                    TextInput::make('rating_user_count')
                        ->label(__('Rating User Count'))
                        ->required()
                        ->numeric()
                        ->default(0),
                    TextInput::make('total_comments')
                        ->label(__('Total Comments'))
                        ->required()
                        ->numeric()
                        ->default(0),
                    TextInput::make('visit_count')
                        ->label(__('Visit Count'))
                        ->required()
                        ->numeric()
                        ->default(0),
                ])->columns(2),

            Section::make(__('Departments & Ranks'))
                ->schema([
                    Select::make('institute_id')
                        ->required()
                        ->label(__('Institute'))
                        ->searchable()
                        ->preload(20)
                        ->options(Institute::all()->pluck('localizedName', 'id')),
                    Select::make('department_id')
                        ->required()
                        ->label(__('Department'))
                        ->searchable()
                        ->preload(20)
                        ->options(Department::all()->pluck('localizedTitle', 'id')),
                    Select::make('specialty_id')
                        ->required()
                        ->label(__('Specialty'))
                        ->searchable()
                        ->preload(20)
                        ->options(Specialty::all()->pluck('localizedTitle', 'id')),
                    Select::make('subspecialty_id')
                        ->required()
                        ->label(__('SubSpecialty'))
                        ->searchable()
                        ->preload(20)
                        ->options(Subspecialty::all()->pluck('localizedTitle', 'id')),
                    Select::make('rank_id')
                        ->required()
                        ->label(__('Rank'))
                        ->searchable()
                        ->preload(20)
                        ->options(Rank::all()->pluck('localizedTitle', 'id')),
                    TextInput::make('schs')
                        ->label('SCHS'),
                ])->columns(2),
        ];
    }
}