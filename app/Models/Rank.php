<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Rank extends Model
{
    use SoftDeletes;
    protected $casts = [
        'title' => 'array',
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }
}
