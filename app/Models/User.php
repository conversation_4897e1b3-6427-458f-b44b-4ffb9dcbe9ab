<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Models\Contracts\HasName;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements FilamentUser, HasName
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'full_name'=>'array',
            'name'=>'array',
        ];
    }

    public function getFilamentName(): string
    {
        return $this->full_name[app()->getLocale()] ?? reset($this->full_name);
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return str_ends_with($this->email, '@qayimdoctory.com');
    }

    public function localizedName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->full_name[app()->getLocale()] ?? reset($this->full_name)
        );
    }
}
