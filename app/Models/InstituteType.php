<?php

namespace App\Models;

use App\Enums\InstituteTypeStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class InstituteType extends Model
{
    use SoftDeletes;
    protected $casts = [
        'title'  => 'array',
        'status' => InstituteTypeStatus::class,
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }
}
