<?php

namespace App\Models;

use App\Enums\CityStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class City extends Model
{
    protected $casts = [
        'title' => 'array',
        'status'=>CityStatus::class
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class,'country_code','code');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class,'state_code','state_code');
    }
}
