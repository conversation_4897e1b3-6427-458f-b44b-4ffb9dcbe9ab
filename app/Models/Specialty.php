<?php

namespace App\Models;

use App\Enums\SpecialtyStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Specialty extends Model
{
    protected $casts = [
        'title' => 'array',
        'status'=>SpecialtyStatus::class
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }
}
