<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstituteMessage extends Model
{

    public function institute(): BelongsTo
    {
        return $this->belongsTo(Institute::class);
    }

    public function FullPhone(): Attribute
    {
        return Attribute::make(
            get: fn() => "{$this->dialing_code} {$this->phone}"
        );
    }

    protected static function booted(): void
    {
        parent::booted();
        static::creating(function ($instituteMessage) {
            if (empty($instituteMessage->dialing_code)) {
                $instituteMessage->dialing_code = '+966';
            }
        });
    }
}
