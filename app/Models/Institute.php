<?php

namespace App\Models;

use App\Filament\Forms\DoctorForm;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Enums\InstituteStatus;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Institute extends Model implements HasMedia
{
    use InteractsWithMedia, SoftDeletes;

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
        'status'=>InstituteStatus::class
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('institutes_logo')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg']);
        $this->addMediaCollection('institutes_banner')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg']);
    }

    public function getLogoAttribute($value): string
    {
        $baseUrl = 'https://api.qayimdactory.com';
        $storageUrl = $baseUrl . '/storage/';
        $defaultLogo = $baseUrl . '/images/institute_default.jpg';

        return empty($value) ? $defaultLogo : $storageUrl . $value;
    }

    public function getBannerAttribute($value): string
    {
        $baseUrl = 'https://api.qayimdactory.com';
        $storageUrl = $baseUrl . '/storage/';
        $defaultBanner = $baseUrl . '/images/profile/clinic-banner.jpg';

        return empty($value) ? $defaultBanner : $storageUrl . $value;
    }

    protected static function booted(): void
    {
        parent::booted();
        static::creating(function ($institute) {
            if (empty($institute->dialing_code)) {
                $institute->dialing_code = '+966';
            }
        });
    }

    public function getSlugAttribute(): string
    {
        return str_replace(' ', '-', $this->name['ar']);
    }

    public function instituteType(): BelongsTo
    {
        return $this->belongsTo(InstituteType::class);
    }
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_code', 'code');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_code', 'state_code');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function FullPhone(): Attribute
    {
        return Attribute::make(
            get: fn() => "{$this->dialing_code} {$this->other_phone}"
        );
    }

    public function instituteMessages(): HasMany
    {
        return $this->hasMany(InstituteMessage::class);
    }

    public function localizedName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->name[app()->getLocale()] ?? reset($this->name)
        );
    }

    public function doctors(): HasMany
    {
        return $this->hasMany(Doctor::class);
    }

    public function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->description[app()->getLocale()] ?? null
        );
    }
}
