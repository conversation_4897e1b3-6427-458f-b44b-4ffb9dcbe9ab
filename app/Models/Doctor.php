<?php

namespace App\Models;

use App\Enums\DoctorStatus;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Doctor extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $casts = [
        'status' => DoctorStatus::class,
        'salutation'=>'array',
        'profile_url'=>'array',
        'first_name'=>'array',
        'last_name'=>'array',
        'middle_name'=>'array',
        'full_name'=>'array',
    ];

    protected static function booted(): void
    {
        parent::booted();
        static::creating(function ($institute) {
            if (empty($institute->dialing_code)) {
                $institute->dialing_code = '+966';
            }
        });
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('doctors_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg']);
    }

    public function getProfilePhotoUrlAttribute()
    {
        //$baseUrl = 'https://api.qayimdactory.com';
        $baseUrl = 'https://fls-9ec92723-3c21-4837-91e6-71b0bd958e7d.laravel.cloud';
        if (empty($this->profile_photo)) {
            return $this->gender === 'female'
                ? 'https://qayim-dactory-main-vgkcyi.laravel.cloud' . '/images/profile/f-doctor.jpg'
                : 'https://qayim-dactory-main-vgkcyi.laravel.cloud' . '/images/profile/m-doctor.jpg';
        }

        if (!str_starts_with($this->profile_photo, $baseUrl)) {
            return $baseUrl . '/' . $this->profile_photo;
        }

        return $this->profile_photo;
    }

    public function getSlugAttribute(): string
    {
        return str_replace(' ', '-', $this->full_name['ar']);
    }

    public function institute(): BelongsTo
    {
        return $this->belongsTo(Institute::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function specialty(): BelongsTo
    {
        return $this->belongsTo(Specialty::class);
    }

    public function subSpecialty(): BelongsTo
    {
        return $this->belongsTo(Subspecialty::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }
    public function rating(): HasMany
    {
        return $this->hasMany(Rating::class);
    }
    public function ratingDetails(): HasMany
    {
        return $this->hasMany(RatingDetail::class, 'doctor_id');
    }

    public function kpis(): HasMany
    {
        return $this->hasMany(Kpi::class, 'department_id', 'department_id')
            ->where('status', 'active');
    }

    public function doctorKpiWiseRating()
    {
        $ratingIds = $this->rating()->pluck('id');

        return $this->kpis()
            ->with([
                'ratingDetails' => function ($query) use ($ratingIds) {
                    $query->whereIn('rating_id', $ratingIds)
                        ->selectRaw('kpi_no, doctor_id, COALESCE(AVG(score_doctor + score_other), 0) as avg_rating')
                        ->groupBy('kpi_no', 'doctor_id');
                }
            ])
            ->get()
            ->map(fn($kpi) => [
                'id' => $kpi->id,
                'kpi_no' => $kpi->kpi_no,
                'title' => $kpi->title,
                'title_remote' => $kpi->title_remote,
                'avg_rating' => number_format($kpi->ratingDetails->first()?->avg_rating ?? 0, 2),
                'percent' => intval((($kpi->ratingDetails->first()?->avg_rating ?? 0) / 5) * 100),
            ]);
    }


    public function rank(): BelongsTo
    {
        return $this->belongsTo(Rank::class);
    }

    public function localizedName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->full_name[app()->getLocale()] ?? reset($this->full_name)
        );
    }

    public function scopeWithSpecialty(Builder $query): Builder
    {
        return $query->whereHas('specialty');
    }

    public function scopeWithInstitute(Builder $query): Builder
    {
        return $query->whereHas('institute');
    }

    public function scopeWithCity(Builder $query): Builder
    {
        return $query->whereHas('city');
    }

    public function scopeWithRelations(Builder $query): Builder
    {
        return $query->with([
            'department:id,title',
            'specialty:id,title',
            'city:id,title',
            'institute:id,name',
        ]);
    }

    public function scopeTopRated(Builder $query, int $limit = 10): Builder
    {
        return $query->orderBy('avg_rating', 'desc')->take($limit);
    }
}



