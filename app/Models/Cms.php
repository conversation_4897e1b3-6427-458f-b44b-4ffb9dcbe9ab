<?php

namespace App\Models;

use App\Enums\InstituteStatus;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Cms extends Model
{
    use HasTranslations;

    protected $fillable = [
        'title',
        'content',
        'meta_keywords',
        'meta_description',
        'position',
        'parent',
        'slug',
        'banner_image',
        'sort',
        'status',
    ];

    protected $casts = [
        'title' => 'array',
        'content' => 'array',
        'meta_keywords' => 'array',
        'meta_description' => 'array',
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }
    public function localizedContent(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->content[app()->getLocale()] ?? reset($this->content)
        );
    }
    public function localizedMetaKeywords(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->meta_keywords[app()->getLocale()] ?? reset($this->meta_keywords)
        );
    }
    public function localizedMetaDescription(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->meta_description[app()->getLocale()] ?? reset($this->meta_description)
        );
    }

    public function children()
    {
        return $this->hasMany(Cms::class, 'parent');
    }
}
