<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Kpi extends Model
{

    protected $casts = [
        'title'=>'array',
        'title_remote'=>'array',
    ];

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function ratingDetails(): HasMany
    {
        return $this->hasMany(RatingDetail::class, 'kpi_no', 'kpi_no');
    }
}
