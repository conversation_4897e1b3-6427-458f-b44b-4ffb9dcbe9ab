<?php

namespace App\Models;

use App\Enums\SubSpecialtyStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Subspecialty extends Model
{
    use SoftDeletes;
    protected $casts = [
        'title' => 'array',
        'status' => SubSpecialtyStatus::class,
    ];

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }
}
