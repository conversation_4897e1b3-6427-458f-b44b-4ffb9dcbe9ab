<?php

namespace App\Models;

use App\Enums\DepartmentStatus;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Department extends Model implements HasMedia
{
    use InteractsWithMedia, SoftDeletes;

    protected $casts = [
        'title'  => 'array',
        'status' => DepartmentStatus::class
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('departments_icons')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml']);
    }

    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }

    public function getIconAttribute(): string
    {
        $icons = [
            62 => 'https://qayimdactory.com/assets/img/dentistry.svg',
            60 => 'https://qayimdactory.com/assets/img/cardiology.svg',
            71 => 'https://qayimdactory.com/assets/img/general_surgery.svg',
            84 => 'https://qayimdactory.com/assets/img/ophthalmology.svg',
        ];

        return $icons[$this->id] ?? 'https://qayimdactory.com/assets/img/cardiology.svg';
    }

    public function getDescriptionAttribute(): string
    {
        $descriptions = [
            62 => 'أفضل أطباء الأسنان وأسنان الأطفال وجميع تخصصات الأسنان بحسب آراء وتجارب المراجعين',
            60 => 'أفضل أطباء تخصص أمراض القلب بحسب آراء وتجارب المراجعين',
            71 => 'أفضل أطباء تخصص الجراحة العامة وجراحة المناظير بحسب آراء وتجارب المراجعين',
            84 => 'أفضل أطباء تخصص العيون والشبكية وجراحة الليزر و تصحيح النظر بحسب آراء وتجارب المراجعين',
        ];

        return $descriptions[$this->id] ?? 'أفضل الأطباء بحسب آراء وتجارب المراجعين';
    }

}
