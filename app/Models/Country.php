<?php

namespace App\Models;

use App\Enums\CountryStatus;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Country extends Model implements HasMedia
{
    use InteractsWithMedia,SoftDeletes;

    protected $casts = [
        'title' => 'array',
        'capital_name'=>'array',
        'status'=>CountryStatus::class
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('countries_icons')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg']);
    }
    public function localizedTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->title[app()->getLocale()] ?? reset($this->title)
        );
    }
}
