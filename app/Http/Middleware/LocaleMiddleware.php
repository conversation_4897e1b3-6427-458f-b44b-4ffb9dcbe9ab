<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class LocaleMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->hasHeader('Locale')) {
            App::setLocale($request->header('Locale'));
        }

        return $next($request);
    }
}

