<?php

namespace App\Http\Controllers;

use App\Models\Institute;
use Illuminate\Http\Request;

class InstituteController extends Controller
{
    public function index()
    {
        $institutes = Institute::with(['instituteType', 'country', 'city', 'state'])
            ->paginate(10);
        return view('institutes.index', compact('institutes'));
    }

    public function show(Institute $institute)
    {
        $institute->load(['country:id,title', 'state:id,title', 'city:id,title']);
        $doctors = $institute->doctors()
            ->select([
                'id', 'full_name', 'profile_photo', 'profile_url', 'avg_rating', 'rating_count', 'department_id',
                'specialty_id','city_id'
            ])
            ->with([
                'city:id,title',
                'department:id,title',
                'rating',
                'specialty:id,title'
            ])
            ->orderByDesc('avg_rating')
            ->paginate(12);

        return view('institutes.show', compact('institute', 'doctors'));
    }
}
