<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Doctor;
use App\Models\Rating;
use App\Models\Setting;
use App\Models\Institute;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;

class HomeController extends Controller
{

    public function index()
    {
        $userCount = User::count();
        $doctorCount = Doctor::count();
        $clinicCount = Institute::count();
        $reviews = Rating::count();
        $topRatedDoctors = $this->getTopRatedDoctors();
        $departments = $this->getDepartments();
        $comments = $this->getComments();

        return view('home', compact(
            'userCount',
            'doctorCount',
            'clinicCount',
            'reviews',
            'topRatedDoctors',
            'departments',
            'comments',
        ));
    }

    private function getTopRatedDoctors(): Collection
    {
        return Doctor::withRelations()
            ->withSpecialty()
            ->withInstitute()
            ->withCity()
            ->topRated()
            ->get();
    }


    private function getDepartments(): Collection
    {
        return Department::select('id', 'title', 'icon')
            ->whereIn('id', [62, 60, 71, 84])
            ->take(4)
            ->get();
    }

    private function getComments(): Collection
    {
        return Rating::withComment()
            ->take(8)
            ->get();
    }
}
