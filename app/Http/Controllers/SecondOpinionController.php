<?php

namespace App\Http\Controllers;

use App\Models\SecondOpinion;
use Illuminate\Http\Request;

class SecondOpinionController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'phone_number' => ['required', 'string', 'regex:/^\+966[0-9]{9}$/'],
        ], [
            'phone_number.required' => 'رقم الهاتف مطلوب',
            'phone_number.regex' => 'رقم الهاتف غير صحيح',
        ]);

        try {
            SecondOpinion::create([
                'phone_number' => $request->phone_number,
                'status' => 'pending',
            ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حفظ البيانات'
            ], 500);
        }
    }
}
