<?php

namespace App\Http\Controllers;

use App\Models\Doctor;
use App\Models\Institute;
use Illuminate\Http\Request;

class SitemapController extends Controller
{
    public function index()
    {
        // Fetch all doctors and map them to URLs
        $doctors = Doctor::latest()->get()->map(function ($doctor) {
            return [
                'loc' => route('doctors.show', ['doctor' => $doctor->id, 'slug' => $doctor->slug]),
                'lastmod' => optional($doctor->updated_at ?? $doctor->created_at)->toAtomString(),
                'changefreq' => 'weekly',
                'priority' => '0.9',
            ];
        });

        $institutes = Institute::latest()->get()->map(function ($institute) {
            return [
                'loc' => route('institutes.show', ['institute' => $institute->id, 'slug' => $institute->slug]),
                'lastmod' => optional($institute->updated_at ?? $institute->created_at)->toAtomString(),
                'changefreq' => 'weekly',
                'priority' => '0.9',
            ];
        });

        $urls = array_merge($doctors->toArray());

        return response()->view('sitemap', compact('urls'))->header('Content-Type', 'text/xml');
    }
}
