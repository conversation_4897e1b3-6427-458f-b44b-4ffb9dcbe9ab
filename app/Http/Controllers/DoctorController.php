<?php

namespace App\Http\Controllers;

use App\Models\Doctor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class DoctorController extends Controller
{
    public function index()
    {
        $doctors = Doctor::with(['institute', 'user', 'department', 'specialty', 'subSpecialty', 'city', 'rank'])
            ->paginate(10);
        return view('doctors.index', compact('doctors'));
    }

    public function show(Doctor $doctor)
    {
        $similarDoctors = $this->getSimilarDoctors($doctor);
        $reviews = $this->getDoctorReviews($doctor);

        $kpis = $doctor->doctorKpiWiseRating();

        return view('doctors.show', compact('doctor', 'similarDoctors', 'reviews','kpis'));
    }

    private function getSimilarDoctors(Doctor $doctor)
    {
        return Doctor::where('department_id', $doctor->department_id)
            ->where('id', '!=', $doctor->id)
            ->withRelations()
            ->withSpecialty()
            ->withInstitute()
            ->withCity()
            ->take(3)
            ->get();
    }

    private function getDoctorReviews(Doctor $doctor)
    {
        return $doctor->rating()
            ->with('user:id,full_name')
            ->whereNotNull('comment')
            ->with(['user:id,full_name'])
            ->whereHas('user', function ($query) {
                $query->whereNotNull('full_name->ar')
                    ->where('full_name->ar', '<>', '')
                    ->whereNotNull('full_name->en')
                    ->where('full_name->en', '<>', '');
            })
            ->paginate(3);
    }
}
