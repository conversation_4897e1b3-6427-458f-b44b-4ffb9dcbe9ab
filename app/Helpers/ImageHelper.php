<?php

namespace App\Helpers;

use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    public static function optimizeImage($path, $width = null, $height = null)
    {
        $originalPath = public_path($path);
        $webpPath = str_replace(['.jpg', '.jpeg', '.png'], '.webp', $originalPath);
        $avifPath = str_replace(['.jpg', '.jpeg', '.png'], '.avif', $originalPath);

        // Create WebP version
        $image = Image::make($originalPath);
        if ($width && $height) {
            $image->resize($width, $height, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
        $image->encode('webp', 80)->save($webpPath);

        // Create AVIF version (if supported)
        if (function_exists('imageavif')) {
            $image->encode('avif', 80)->save($avifPath);
        }

        return [
            'original' => $path,
            'webp' => str_replace(['.jpg', '.jpeg', '.png'], '.webp', $path),
            'avif' => str_replace(['.jpg', '.jpeg', '.png'], '.avif', $path),
        ];
    }

    public static function getResponsiveImage($path, $alt, $class = '', $sizes = '100vw')
    {
        $optimized = self::optimizeImage($path);
        
        return view('components.responsive-image', [
            'original' => $optimized['original'],
            'webp' => $optimized['webp'],
            'avif' => $optimized['avif'],
            'alt' => $alt,
            'class' => $class,
            'sizes' => $sizes
        ])->render();
    }
} 