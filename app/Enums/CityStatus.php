<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum CityStatus: string implements HasColor, HasIcon, HasLabel
{
    case Active = 'active';
    case Inactive = 'inactive';

    public static function toArray(): array
    {
        return [
            self::Active->value   => __('Active'),
            self::Inactive->value => __('Inactive'),
        ];
    }
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Active => 'success',
            self::Inactive => 'danger',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Active => 'tabler-progress-check',
            self::Inactive => 'tabler-circle-dashed',
        };
    }
    public function getLabel(): ?string
    {
        return __($this->value);
    }
}
