<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum DoctorStatus: string implements HasColor, HasIcon, HasLabel
{
    case Approved = 'approved';
    case Rejected = 'rejected';
    case Pending = 'pending';

    public static function toArray(): array
    {
        return [
            self::Approved->value => __('Approved'),
            self::Rejected->value => __('Rejected'),
            self::Pending->value => __('Pending'),
        ];
    }
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Approved => 'success',
            self::Rejected => 'danger',
            self::Pending => 'info',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Approved => 'tabler-progress-check',
            self::Rejected => 'tabler-circle-dashed-x',
            self::Pending => 'tabler-circle-dashed',
        };
    }
    public function getLabel(): ?string
    {
        return __($this->value);
    }
}
