<?php
$applications = json_decode(file_get_contents(asset('json/Application/online_conultant.json')), true);
foreach($applications as $key => $data) {
    $ct = Application::query()->updateOrCreate(
        ['id' => $data['APP_ID']],
        [
            'title' => [
                'en' => $data['APP_NAME'],
                'ar' => $data['APP_NAME_AR'],
            ],
            'id' => $data['APP_ID'],
            'sort' => $data['ID'],
        ]);
}
