<?php
$languages = ['none', 'en', 'ar'];
$cat_details = json_decode(file_get_contents(asset('json/Speciality/CAT_DETAILS.json')), true);
foreach ($cat_details as $key => $data) {
    $speciality = Specialty::query()->updateOrCreate(
        ['id' => $data['CAT_ID']],
        [
            'id' => $data['CAT_ID'],
            'department_id' => $data['DEPT_ID']
        ]
    );

    $x = Specialty::query()->findOrFail($data['CAT_ID'])
        ->setTranslation('title', $languages[$data['LANG_ID']], $data['CAT_NAME'])
        ->save();
}
