<?php
//Master Table
$dept_m = json_decode(file_get_contents(asset('json/Department/dept_m.json')), true);
foreach ($dept_m as $key => $data) {
    Department::query()->updateOrCreate(
        ['id' => $data['DEPT_ID']],
        [
            'id' => $data['DEPT_ID'],
            'sort' => $data['LIST'] ?? 999999,
            'title' => null,
        ]
    );
}

$languages = [
    'none',
    'en',
    'ar'
];

//details table having language related title
$dept_d = json_decode(file_get_contents(asset('json/Department/dept_d.json')), true);

foreach ($dept_d as $key => $data) {
    $x = Department::query()->findOrFail($data['DEPT_M_ID'])
        ->setTranslation('title', $languages[$data['LANG_ID']], $data['TEXT'])
        ->save();
}

//another detail for having image
$department_details = json_decode(file_get_contents(asset('json/Department/department_details.json')), true);
foreach ($department_details as $key => $data) {

    $x = Department::query()->updateOrCreate(
        ['id' => $data['DEPT_ID']],
        [
            'icon' => (is_null($data['ICON']) || $data['ICON'] == '') ? null : 'images/departments/'.$data['ICON']
        ]
    );
}
