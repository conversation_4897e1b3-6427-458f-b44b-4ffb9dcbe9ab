<?php
$ranks_m = json_decode(file_get_contents(asset('json/Rank/rank_m.json')), true);
foreach($ranks_m as $key => $rank) {
    $ct = Rank::query()->updateOrCreate(
        ['id' => $rank['RANK_ID']],
        [
            'title' => [
                'en' => $rank['RANK_NAME'],
                'ar' => $rank['RANK_NAME'],
            ],
            'id' => $rank['RANK_ID']
        ]);
}

$ranks_d = json_decode(file_get_contents(asset('json/Rank/rank_d.json')), true);
$languages = [
    'none',
    'en',
    'ar'
];

foreach($ranks_d as $key => $rank) {
    Rank::query()->findOrFail($rank['RANK_ID_M'])
        ->setTranslation('title', $languages[$rank['LANG_ID']], $rank['TEXT'])
        ->save();
}
