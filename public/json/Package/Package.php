<?php
$packages = json_decode(file_get_contents(asset('json/Package/packages.json')), true);
//return $packages;
foreach($packages as $key => $data) {
    DB::statement('SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";');
    $package_type = ($data['PLAN_TYPE'] === 'DOC') ? config('ws_role.doctor') : config('ws_role.institute');
    $ct = Package::query()->updateOrCreate(
        ['id' => $data['PLAN_ID']],
        [
            'id' => $data['PLAN_ID'],
            'title' => [
                'en' => $data['PlanName'],
                'ar' => $data['PlanName'],
            ],
            'sub_title' => [
                'en' => '',
                'ar' => '',
            ],
            'discount_text' => [
                'en' => '',
                'ar' => '',
            ],
            'package_type' => $package_type,
            'currency_code' => config('ws_unit.default_currency'),
            'level' => $data['Pack_Level'],
            'zoho_item_id' => $data['ZOHO_ITEM_ID'],
            'price' => $data['PRICE'],
            'tax' => 15,
            'duration' => $data['Duration'],
            'is_recursive' => 1,
            'status' => config('ws_status.active'),
            'description' => [
                'en' => '',
                'ar' => '',
            ]
        ]
    );
}
