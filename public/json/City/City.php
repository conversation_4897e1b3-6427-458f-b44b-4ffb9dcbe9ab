$states = State::all()->keyBy('id');
    $cities_m = json_decode(file_get_contents(asset('json/city_m.json')), true);
    foreach($cities_m as $key => $city) {
        $ct = City::query()->updateOrCreate(
            ['id' => $city['CITY_ID']],
            [
                'country_code' => $states[$city['REGION']]['country_code'],
                'state_code' => $states[$city['REGION']]['state_code'],
                'title' => [
                    'en' => $city['NAME'],
                    'ar' => $city['NAME'],
                ],
                'id' => $city['CITY_ID']
            ]);
    }

    $cities_d = json_decode(file_get_contents(asset('json/city_d.json')), true);
    $languages = [
        'none',
        'en',
        'ar'
    ];
    foreach($cities_d as $key => $city) {
        City::query()->findOrFail($city['CITY_ID_M'])
            ->setTranslation('title', $languages[$city['LANG_ID']], $city['TEXT'])
            ->save();
    }
