<?php
$inst_type_m = json_decode(file_get_contents(asset('json/inst_type/inst_type_m.json')), true);
foreach($inst_type_m as $key => $rank) {
    $ct = InstituteType::query()->updateOrCreate(
        ['id' => $rank['INST_TYPE_ID']],
        [
            'title' => [
                'en' => $rank['NAME'],
                'ar' => $rank['NAME'],
            ],
            'id' => $rank['INST_TYPE_ID']
        ]);
}

$languages = [
    'none',
    'en',
    'ar'
];

$inst_type_d = json_decode(file_get_contents(asset('json/inst_type/inst_type_d.json')), true);
foreach($inst_type_d as $key => $inst_type) {
    InstituteType::query()->findOrFail($inst_type['INST_TYPE_MAIN_ID'])
        ->setTranslation('title', $languages[$inst_type['LANG_ID']], $inst_type['TEXT'])
        ->save();
}
