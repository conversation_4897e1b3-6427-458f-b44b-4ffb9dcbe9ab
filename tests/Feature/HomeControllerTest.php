<?php

namespace Tests\Feature;

use App\Models\Rating;
use Database\Factories\UserFactory;
use Database\Factories\DoctorFactory;
use Database\Factories\RatingFactory;
use Database\Factories\InstituteFactory;
use Database\Factories\DepartmentFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class HomeControllerTest extends TestCase
{

    public function test_home_page_displays_statistics_and_sections(): void
    {
//        UserFactory::new()->count(5)->create();
//        DoctorFactory::new()->count(5)->create();
//        InstituteFactory::new()->count(3)->create();
//        DepartmentFactory::new()->count(6)->create();
//        RatingFactory::new()->count(10)->create();
//        RatingFactory::new()->count(5)->create(['comment' => 'Great service!']);
//        $response = $this->get(route('home'))->assertSuccessful();
//
//        $response->assertViewHas([
//            'userCount',
//            'doctorCount',
//            'clinicCount',
//            'reviews',
//            'topRatedDoctors',
//            'departments',
//            'comments'
//        ]);
//        $departments = $response->viewData('departments');
//        $this->assertCount(4, $departments);
//        $topRatedDoctors = $response->viewData('topRatedDoctors');
//        $this->assertLessThanOrEqual(10, $topRatedDoctors->count());
    }
}
